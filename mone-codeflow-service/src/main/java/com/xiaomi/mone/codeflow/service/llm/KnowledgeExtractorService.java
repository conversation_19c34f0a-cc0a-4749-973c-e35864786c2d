package com.xiaomi.mone.codeflow.service.llm;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class KnowledgeExtractorService {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 从大模型输出中提取术语及关键词列表，兼容非标准Json格式
     * @param modelOutput 大模型输出
     * @return 术语及关键词列表
     */
    public List<KnowledgeQuery> extractKnowledgeQueries(String modelOutput) {
        List<KnowledgeQuery> result = new ArrayList<>();
        String json = null;
        try {
            json = extractJsonBlock(modelOutput);
            if (json != null) {
                result = OBJECT_MAPPER.readValue(json, new TypeReference<List<KnowledgeQuery>>() {});
            }
        } catch (Exception e) {
            log.warn("解析术语Json失败，返回空列表，原始大模型输出：\n{}\n提取到的json片段：\n{}", modelOutput, json, e);
        }
        return result;
    }

    /**
     * 尽可能健壮地从大模型输出中提取JSON内容
     */
    private String extractJsonBlock(String text) {
        if (text == null) return null;
        // 1. 优先提取```json ... ```或``` ... ```代码块
        Pattern codeBlockPattern = Pattern.compile("```(?:json|JSON)?\\s*([\\s\\S]*?)\\s*```", Pattern.CASE_INSENSITIVE);
        Matcher matcher = codeBlockPattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        // 2. 没有代码块，找第一个[或{，最后一个]或}
        int arrayStart = text.indexOf('[');
        int arrayEnd = text.lastIndexOf(']');
        if (arrayStart != -1 && arrayEnd != -1 && arrayEnd > arrayStart) {
            return text.substring(arrayStart, arrayEnd + 1).trim();
        }
        int objStart = text.indexOf('{');
        int objEnd = text.lastIndexOf('}');
        if (objStart != -1 && objEnd != -1 && objEnd > objStart) {
            return text.substring(objStart, objEnd + 1).trim();
        }
        // 3. 都没有，返回原文
        return text.trim();
    }

    @Data
    public static class KnowledgeQuery {
        private String term;
        private List<String> keywords;
        private List<String> questions;
    }
} 