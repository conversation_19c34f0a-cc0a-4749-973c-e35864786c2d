package com.xiaomi.mone.codeflow.service.feishu;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.xiaomi.mone.codeflow.api.dto.FeiShuAuthResponseDTO;
import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuJsApiTicketService;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuUserAccessTokenService;
import com.xiaomi.mone.codeflow.service.feishu.results.FeiShuUserAccessTokenResult;
import com.xiaomi.mone.codeflow.service.cas.UserService;
import com.xiaomi.mone.codeflow.dto.user.UserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Slf4j
@Service
public class FeiShuAuthParamsService {

    private final FeiShuUserInfoService feiShuUserInfoService;
    private final FeiShuUserAccessTokenService feiShuUserAccessTokenService;
    private final FeiShuRefreshUserAccessTokenService feiShuRefreshUserAccessTokenService;
    private final FeiShuJsApiTicketService feiShuJsApiTicketService;
    private final UserService userService;
    private final FeiShuStorageRedisService feiShuStorageRedisService;

    @NacosValue("${feishu.api.appId:}")
    private String appId;

    public FeiShuAuthParamsService(
            FeiShuUserInfoService feiShuUserInfoService,
            FeiShuUserAccessTokenService feiShuUserAccessTokenService,
            FeiShuRefreshUserAccessTokenService feiShuRefreshUserAccessTokenService,
            FeiShuJsApiTicketService feiShuJsApiTicketService,
            UserService userService,
            FeiShuStorageRedisService feiShuStorageRedisService) {
        this.feiShuUserInfoService = feiShuUserInfoService;
        this.feiShuUserAccessTokenService = feiShuUserAccessTokenService;
        this.feiShuRefreshUserAccessTokenService = feiShuRefreshUserAccessTokenService;
        this.feiShuJsApiTicketService = feiShuJsApiTicketService;
        this.userService = userService;
        this.feiShuStorageRedisService = feiShuStorageRedisService;
    }

    public FeiShuAuthResponseDTO getAuthParams(String account, String feiShuCode, String pageUrl) throws Exception {
        String accessToken;
        
        // 1. 如果feiShuCode不为空，强制更新token
        if (feiShuCode != null && !feiShuCode.isEmpty()) {
            log.info("FeiShuCode provided, getting new token for user: {}", account);
            FeiShuUserAccessTokenResult userAccessTokenResult = feiShuUserAccessTokenService.getUserAccessToken(feiShuCode);
            accessToken = userAccessTokenResult.getAccessToken();
            if (accessToken == null) {
                throw new RuntimeException("Failed to get access token with feiShuCode");
            }
            
            // 保存新token到Redis
            feiShuStorageRedisService.saveAccessToken(account, accessToken, userAccessTokenResult.getExpiresIn());
            feiShuStorageRedisService.saveRefreshToken(account, userAccessTokenResult.getRefreshToken(), userAccessTokenResult.getRefreshExpiresIn());
        } else {
            // feiShuCode为空，从Redis获取token
            accessToken = feiShuStorageRedisService.getAccessToken(account);
            String refreshToken = feiShuStorageRedisService.getRefreshToken(account);
            
            if (accessToken == null) {
                if (refreshToken != null) {
                    // accessToken不存在但refreshToken存在，刷新token
                    log.info("Access token not found but refresh token exists, refreshing token for user: {}", account);
                    accessToken = refreshAndSaveToken(account, refreshToken);
                } else {
                    // 两个token都不存在，抛出异常
                    log.error("No tokens found in Redis for user: {}", account);
                    throw new RuntimeException("Feishu access_token and refresh_token not found");
                }
            } else {
                log.info("Using cached access token from Redis for user: {}", account);
            }
        }

        // 2. 获取jsapi_ticket
        String ticket = feiShuJsApiTicketService.getJsApiTicket(accessToken);
        if (ticket == null) {
            throw new RuntimeException("Failed to get jsapi ticket");
        }

        // 3. 生成Signature
        long timestamp = System.currentTimeMillis();
        String nonceStr = RandomStringUtils.randomAlphanumeric(16);

        String string1 = String.format("jsapi_ticket=%s&noncestr=%s&timestamp=%d&url=%s",
                ticket, nonceStr, timestamp, pageUrl);
        String signature = sha1(string1);

        return FeiShuAuthResponseDTO.builder()
                .openId(feiShuUserInfoService.getUserInfo(accessToken).getData().getOpenId())
                .signature(signature)
                .appId(appId)
                .timestamp(timestamp)
                .nonceStr(nonceStr)
                .url(pageUrl)
                .build();
    }

    private String sha1(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-1 algorithm not found", e);
        }
    }

    /**
     * 刷新用户token并保存到Redis
     * @param account 用户账号
     * @param refreshToken 刷新token
     * @return 新的access token
     * @throws RuntimeException 当刷新token失败时抛出异常
     */
    public String refreshAndSaveToken(String account, String refreshToken) throws Exception {
        log.info("Refreshing token for user: {}", account);
        FeiShuUserAccessTokenResult refreshResult = feiShuRefreshUserAccessTokenService.refreshUserAccessToken(refreshToken);
        if (refreshResult.getCode() == ResultCodeEnum.SUCCESS) {
            String newAccessToken = refreshResult.getAccessToken();
            String newRefreshToken = refreshResult.getRefreshToken();
            
            // 保存新的token到Redis
            feiShuStorageRedisService.saveAccessToken(account, newAccessToken, refreshResult.getExpiresIn());
            feiShuStorageRedisService.saveRefreshToken(account, newRefreshToken, refreshResult.getRefreshExpiresIn());
            
            return newAccessToken;
        } else {
            log.error("Failed to refresh token for user {}: {}", account, refreshResult.getMsg());
            throw new RuntimeException("Failed to refresh token: " + refreshResult.getMsg());
        }
    }

    /**
     * 从Redis获取access_token，如果不存在则尝试使用refresh_token刷新
     * @param account 用户账号
     * @return access_token
     * @throws RuntimeException 当获取token失败时抛出异常
     */
    public String getAccessTokenByRedis(String account) throws Exception {
        String accessToken = feiShuStorageRedisService.getAccessToken(account);
        String refreshToken = feiShuStorageRedisService.getRefreshToken(account);
        
        if (accessToken == null && refreshToken == null) {
            throw new RuntimeException("Feishu access_token and refresh_token not found");
        }

        // 如果access_token不存在但refresh_token存在，则刷新token
        if (accessToken == null && refreshToken != null) {
            log.info("Access token not found but refresh token exists, refreshing token for user: {}", account);
            accessToken = refreshAndSaveToken(account, refreshToken);
        }

        return accessToken;
    }
}