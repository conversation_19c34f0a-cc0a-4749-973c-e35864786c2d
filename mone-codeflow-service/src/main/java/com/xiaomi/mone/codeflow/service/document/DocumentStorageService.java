package com.xiaomi.mone.codeflow.service.document;

import com.xiaomi.mone.codeflow.dto.common.PageResult;
import com.xiaomi.mone.codeflow.dto.document.RdDocumentRecordDto;
import com.xiaomi.mone.codeflow.dto.document.RdDocumentRecordDetailDto;

/**
 * 文档存储服务接口
 * 用于将生成的文档内容保存到数据库
 */
public interface DocumentStorageService {

    /**
     * 保存文档
     *
     * @param uid 用户ID
     * @param feishuUrl 飞书文档URL
     * @param rddocUrl 文档生成记录URL
     * @param prdContent PRD内容
     * @param apiDocContent API文档内容
     * @param sqlDocContent SQL文档内容
     * @param knowledgeContent 知识内容
     * @param resultContent 生成结果内容
     * @param meegoUrl Meego需求URL
     * @return 文档ID
     */
    Long saveDocument(String uid, String meegoUrl, String feishuUrl, String rddocUrl, String prdContent,
                     String apiDocContent, String sqlDocContent, String knowledgeContent,
                     String resultContent);

    /**
     * 获取用户文档生成记录分页列表
     *
     * @param uid 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 文档生成记录分页列表
     */
    PageResult<RdDocumentRecordDto> getDocumentsByUid(String uid, int pageNum, int pageSize);

    /**
     * 获取文档详情
     *
     * @param id 文档ID
     * @param uid 用户ID，用于权限验证
     * @return 文档详情DTO，包含全部内容
     */
    RdDocumentRecordDetailDto getDocumentDetail(Long id, String uid);

    /**
     * 删除文档
     *
     * @param id 文档ID
     * @return 删除是否成功
     */
    boolean deleteDocument(Long id);
}