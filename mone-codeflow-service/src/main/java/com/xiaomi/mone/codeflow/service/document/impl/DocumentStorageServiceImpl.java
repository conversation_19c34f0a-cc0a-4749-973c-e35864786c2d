package com.xiaomi.mone.codeflow.service.document.impl;

import com.xiaomi.mone.codeflow.dao.RdDocumentGenerationDao;
import com.xiaomi.mone.codeflow.dto.common.PageResult;
import com.xiaomi.mone.codeflow.dto.document.RdDocumentGenerationRecord;
import com.xiaomi.mone.codeflow.dto.document.RdDocumentRecordDto;
import com.xiaomi.mone.codeflow.dto.document.RdDocumentRecordDetailDto;
import com.xiaomi.mone.codeflow.service.document.DocumentStorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文档存储服务实现类
 */
@Slf4j
@Service
public class DocumentStorageServiceImpl implements DocumentStorageService {

    @Autowired
    private RdDocumentGenerationDao rdDocumentGenerationDao;

    @Override
    @Transactional
    public Long saveDocument(String uid, String meegoUrl, String feishuUrl, String rddocUrl, String prdContent,
                           String apiDocContent, String sqlDocContent, String knowledgeContent,
                           String resultContent) {
        // 参数校验，确保uid不为空
        if (StringUtils.isBlank(uid)) {
            log.error("用户ID不能为空，保存文档失败");
            return null;
        }

        try {
            RdDocumentGenerationRecord record = RdDocumentGenerationRecord.builder()
                    .uid(uid)
                    .meegoUrl(StringUtils.defaultString(meegoUrl, ""))
                    .feishuUrl(feishuUrl)
                    .rddocUrl(rddocUrl)
                    .prdContent(prdContent)
                    .apiDocContent(apiDocContent)
                    .sqlDocContent(sqlDocContent)
                    .knowledge(knowledgeContent)
                    .resultContent(resultContent)
                    .createTime(LocalDateTime.now())
                    .build();

            rdDocumentGenerationDao.save(record);

            log.info("保存文档生成记录成功, id: {}, uid: {}", record.getId(), uid);

            return record.getId();
        } catch (Exception e) {
            log.error("保存文档生成记录失败", e);
            return null;
        }
    }

    @Override
    public PageResult<RdDocumentRecordDto> getDocumentsByUid(String uid, int pageNum, int pageSize) {
        try {
            // 参数校验
            if (StringUtils.isBlank(uid)) {
                log.warn("获取文档生成记录时用户ID为空");
                return PageResult.of(pageNum, pageSize, 0, Collections.emptyList());
            }

            // 获取总记录数
            long total = rdDocumentGenerationDao.countByUid(uid);

            // 如果没有记录，直接返回空结果
            if (total == 0) {
                return PageResult.of(pageNum, pageSize, 0, Collections.emptyList());
            }

            // 计算起始位置
            int offset = (pageNum - 1) * pageSize;

            // 查询分页数据
            List<RdDocumentGenerationRecord> records = rdDocumentGenerationDao.findByUidWithPage(uid, offset, pageSize);

            // 转换为DTO对象
            List<RdDocumentRecordDto> dtoList = records.stream()
                    .map(RdDocumentRecordDto::fromRecord)
                    .collect(Collectors.toList());

            log.info("获取用户文档生成记录成功, uid: {}, 页码: {}, 每页大小: {}, 总记录数: {}", uid, pageNum, pageSize, total);

            // 返回分页结果
            return PageResult.of(pageNum, pageSize, total, dtoList);
        } catch (Exception e) {
            log.error("获取用户文档生成记录失败, uid: {}, 页码: {}, 每页大小: {}", uid, pageNum, pageSize, e);
            return PageResult.of(pageNum, pageSize, 0, Collections.emptyList());
        }
    }

    @Override
    public RdDocumentRecordDetailDto getDocumentDetail(Long id, String uid) {
        try {
            // 参数校验
            if (id == null || id <= 0) {
                log.warn("文档ID无效: {}", id);
                return null;
            }

            if (StringUtils.isBlank(uid)) {
                log.warn("获取文档详情时用户ID为空");
                return null;
            }

            // 获取文档记录
            RdDocumentGenerationRecord record = rdDocumentGenerationDao.findById(id);

            // 记录不存在
            if (record == null) {
                log.warn("文档记录不存在, id: {}", id);
                return null;
            }

            // 权限验证，确保只能查看自己的文档
            if (!StringUtils.equals(uid, record.getUid())) {
                log.warn("无权限查看该文档, id: {}, 文档所有者: {}, 当前用户: {}", id, record.getUid(), uid);
                return null;
            }

            // 转换为详情DTO
            RdDocumentRecordDetailDto detailDto = RdDocumentRecordDetailDto.fromRecord(record);

            log.info("获取文档详情成功, id: {}, uid: {}", id, uid);

            return detailDto;
        } catch (Exception e) {
            log.error("获取文档详情失败, id: {}, uid: {}", id, uid, e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean deleteDocument(Long id) {
        try {
            // 参数校验
            if (id == null || id <= 0) {
                log.warn("删除文档时ID无效: {}", id);
                return false;
            }

            // 先查询文档是否存在
            RdDocumentGenerationRecord record = rdDocumentGenerationDao.findById(id);
            if (record == null) {
                log.warn("要删除的文档不存在, id: {}", id);
                return false;
            }

            // 执行删除操作
            int result = rdDocumentGenerationDao.delete(id);

            // 判断删除是否成功
            boolean success = result > 0;

            if (success) {
                log.info("文档删除成功, id: {}", id);
            } else {
                log.warn("文档删除失败, id: {}", id);
            }

            return success;
        } catch (Exception e) {
            log.error("删除文档时发生异常, id: {}", id, e);
            return false;
        }
    }
}