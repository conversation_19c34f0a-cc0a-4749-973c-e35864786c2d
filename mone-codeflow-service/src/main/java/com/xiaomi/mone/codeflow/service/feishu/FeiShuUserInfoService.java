package com.xiaomi.mone.codeflow.service.feishu;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonParseException;
import com.lark.oapi.okhttp.MediaType;
import com.lark.oapi.okhttp.OkHttpClient;
import com.lark.oapi.okhttp.Request;
import com.lark.oapi.okhttp.Response;
import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import com.xiaomi.mone.codeflow.dto.common.ApiResponse;
import com.xiaomi.mone.codeflow.service.feishu.results.FeiShuUserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class FeiShuUserInfoService extends FeiShuBaseService {

    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build();

    public ApiResponse<FeiShuUserInfoDTO> getUserInfo(String userAccessToken) {
        log.info("FeiShuUserInfoService getUserInfo with userAccessToken: {}", userAccessToken);

        String url = this.getOpenBaseUrl() + "/open-apis/authen/v1/user_info";
        String authHeader = "Bearer " + userAccessToken;

        Request request = new Request.Builder()
                .url(url)
                .get() // Use GET method as per the curl example
                .header("Authorization", authHeader)
                .build();

        try {
            // Simple retry mechanism for 504
            for (int i = 0; i < 2; i++) {
                Response response = HTTP_CLIENT.newCall(request).execute();
                int statusCode = response.code();
                String responseBody = response.body().string();

                log.info("FeiShuUserInfoService getUserInfo response status: {}, body: {}", statusCode, responseBody);

                if (statusCode == 504) {
                    log.error("FeiShuUserInfoService getUserInfo server timeout (504), retrying...");
                    response.close(); // Close the response body to release resources
                    if (i < 1) { // Retry only once
                        continue;
                    } else {
                         return ApiResponse.error("Server timeout (504) after retry");
                    }
                }

                try {
                    JsonObject jsonObject = JsonParser.parseString(responseBody).getAsJsonObject();
                    int apiCode = jsonObject.has("code") ? jsonObject.get("code").getAsInt() : -1;
                    String apiMsg = jsonObject.has("msg") ? jsonObject.get("msg").getAsString() : "";

                    if (apiCode == 0) {
                        if (jsonObject.has("data")) {
                            JsonObject dataObject = jsonObject.getAsJsonObject("data");
                            FeiShuUserInfoDTO.FeiShuUserInfoDTOBuilder dtoBuilder = FeiShuUserInfoDTO.builder();

                            // Extract fields from data object
                            if (dataObject.has("name")) dtoBuilder.name(dataObject.get("name").getAsString());
                            if (dataObject.has("en_name")) dtoBuilder.enName(dataObject.get("en_name").getAsString());
                            if (dataObject.has("avatar_url")) dtoBuilder.avatarUrl(dataObject.get("avatar_url").getAsString());
                            if (dataObject.has("avatar_thumb")) dtoBuilder.avatarThumb(dataObject.get("avatar_thumb").getAsString());
                            if (dataObject.has("avatar_middle")) dtoBuilder.avatarMiddle(dataObject.get("avatar_middle").getAsString());
                            if (dataObject.has("avatar_big")) dtoBuilder.avatarBig(dataObject.get("avatar_big").getAsString());
                            if (dataObject.has("open_id")) dtoBuilder.openId(dataObject.get("open_id").getAsString());
                            if (dataObject.has("union_id")) dtoBuilder.unionId(dataObject.get("union_id").getAsString());
                            if (dataObject.has("email")) dtoBuilder.email(dataObject.get("email").getAsString());
                            if (dataObject.has("enterprise_email")) dtoBuilder.enterpriseEmail(dataObject.get("enterprise_email").getAsString());
                            if (dataObject.has("user_id")) dtoBuilder.userId(dataObject.get("user_id").getAsString());
                            if (dataObject.has("mobile")) dtoBuilder.mobile(dataObject.get("mobile").getAsString());
                            if (dataObject.has("tenant_key")) dtoBuilder.tenantKey(dataObject.get("tenant_key").getAsString());
                            if (dataObject.has("employee_no")) dtoBuilder.employeeNo(dataObject.get("employee_no").getAsString());

                            return ApiResponse.success(dtoBuilder.build());
                        } else {
                            return ApiResponse.error("API response missing data field");
                        }
                    } else {
                        return ApiResponse.error(apiMsg);
                    }
                } catch (JsonParseException e) {
                    log.error("Failed to parse response body as JsonObject: {}", responseBody, e);
                    return ApiResponse.error("Failed to parse API response body");
                } finally {
                    response.close();
                }
            }
        } catch (IOException e) {
            log.error("An error occurred during HTTP request to {}", url, e);
            return ApiResponse.error("HTTP request failed: " + e.getMessage());
        } catch (Exception e) {
             log.error("An unexpected error occurred during API call", e);
             return ApiResponse.error("An unexpected error occurred");
        }

        return ApiResponse.error("API call did not complete successfully.");
    }
} 