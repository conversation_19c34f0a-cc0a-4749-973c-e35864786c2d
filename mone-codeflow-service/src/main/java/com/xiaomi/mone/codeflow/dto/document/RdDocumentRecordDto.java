package com.xiaomi.mone.codeflow.dto.document;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 技术文档生成记录DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RdDocumentRecordDto {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * Meego需求URL
     */
    private String meegoUrl;

    /**
     * 飞书文档URL
     */
    private String feishuUrl;

    /**
     * 技术文档URL
     */
    private String rddocUrl;

    /**
     * PRD内容
     */
    private String prdContent;

    /**
     * API文档内容
     */
    private String apiDocContent;

    /**
     * SQL文档内容
     */
    private String sqlDocContent;

    /**
     * 生成结果内容
     */
    private String resultContent;

    /**
     * 知识库导入信息，JSON字符串
     */
    private String knowledge;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 从完整记录转换为DTO
     *
     * @param record 完整记录
     * @return DTO对象
     */
    public static RdDocumentRecordDto fromRecord(RdDocumentGenerationRecord record) {
        if (record == null) {
            return null;
        }

        return RdDocumentRecordDto.builder()
                .id(record.getId())
                .uid(record.getUid())
                .meegoUrl(record.getMeegoUrl())
                .feishuUrl(record.getFeishuUrl())
                .rddocUrl(record.getRddocUrl())
                .prdContent(record.getPrdContent())
                .apiDocContent(record.getApiDocContent())
                .sqlDocContent(record.getSqlDocContent())
                .knowledge(record.getKnowledge())
                .resultContent(record.getResultContent())
                .createTime(record.getCreateTime())
                .updateTime(record.getUpdateTime())
                .build();
    }
}