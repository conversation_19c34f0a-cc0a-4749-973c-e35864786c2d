package com.xiaomi.mone.codeflow.service.feishu;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonParseException;
import com.lark.oapi.Client;
import com.lark.oapi.service.authen.v1.model.*;
import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import com.xiaomi.mone.codeflow.service.feishu.results.FeiShuUserAccessTokenResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class FeiShuUserAccessTokenService extends FeiShuBaseService {

    public FeiShuUserAccessTokenResult getUserAccessToken(String code) throws Exception {
        log.info("FeiShuUserAccessTokenService getUserAccessToken with code: {}", code);
        
        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();

        // 创建请求对象
        CreateOidcAccessTokenReq req = CreateOidcAccessTokenReq.newBuilder()
                .createOidcAccessTokenReqBody(CreateOidcAccessTokenReqBody.newBuilder()
                        .grantType("authorization_code")
                        .code(code)
                        .build())
                .build();

        // 发起请求
        CreateOidcAccessTokenResp resp = client.authen().v1().oidcAccessToken().create(req);

        // 获取原始响应体
        String rawResponseBody = new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8);
        log.info("FeiShuUserAccessTokenService getUserAccessToken raw response: {}", rawResponseBody);

        FeiShuUserAccessTokenResult result = new FeiShuUserAccessTokenResult();

        try {
            JsonObject jsonObject = JsonParser.parseString(rawResponseBody).getAsJsonObject();
            JsonObject dataObject = jsonObject.getAsJsonObject("data");

            if (dataObject.has("access_token")) {
                result.setAccessToken(dataObject.get("access_token").getAsString());
            }
            if (dataObject.has("expires_in")) {
                result.setExpiresIn(dataObject.get("expires_in").getAsInt());
            }
            if (dataObject.has("refresh_token")) {
                result.setRefreshToken(dataObject.get("refresh_token").getAsString());
            }
            if (dataObject.has("token_type")) {
                result.setTokenType(dataObject.get("token_type").getAsString());
            }
            if (dataObject.has("refresh_expires_in")) {
                result.setRefreshExpiresIn(dataObject.get("refresh_expires_in").getAsInt());
            }
            if (dataObject.has("scope")) {
                result.setScope(dataObject.get("scope").getAsString());
            }

            int apiCode = jsonObject.has("code") ? jsonObject.get("code").getAsInt() : -1;
            String apiMsg = jsonObject.has("message") ? jsonObject.get("message").getAsString() : "";

            if (apiCode != 0) {
                result.setCode(ResultCodeEnum.FAIL);
                result.setMsg(apiMsg);
            }else{
                result.setCode(ResultCodeEnum.SUCCESS);
            }
        } catch (JsonParseException e) {
            log.error("Failed to parse raw response body as JsonObject: {}", rawResponseBody, e);
            result.setCode(ResultCodeEnum.FAIL);
            result.setMsg("Failed to parse API response body");
        } catch (Exception e) {
            log.error("An unexpected error occurred during initial parsing of raw response body: {}", rawResponseBody, e);
            result.setCode(ResultCodeEnum.FAIL);
            result.setMsg("An unexpected error occurred during response parsing");
        }

        return result;
    }
} 