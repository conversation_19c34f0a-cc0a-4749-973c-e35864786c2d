package com.xiaomi.mone.codeflow.service.feishu;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.docx.v1.model.*;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpace;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceReq;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceResp;
import com.xiaomi.mone.codeflow.api.dto.FeiShuDocumentResDto;
import com.xiaomi.mone.codeflow.api.enums.ResCodeEnum;
import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import com.xiaomi.mone.codeflow.service.feishu.entitys.DocumentEntity;
import com.xiaomi.mone.codeflow.service.feishu.entitys.DocumentInfoEntity;
import com.xiaomi.mone.codeflow.service.feishu.results.DocumentResult;
import com.xiaomi.mone.codeflow.service.feishu.results.DocumentInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class FeiShuDocumentService extends FeiShuBaseService{
    public DocumentResult createDocument(DocumentEntity documentEntity) throws Exception {
        if (StringUtils.isBlank(documentEntity.getTitle())|| StringUtils.isBlank(documentEntity.getContent())){
            log.error("createDocument param error");
            return new DocumentResult( ResultCodeEnum.FAIL );
        }
        // 设置默认文件夹
        if(StringUtils.isBlank(documentEntity.getFolderToken())){
            documentEntity.setFolderToken(this.getDefaultFolderToken());
        }
        log.info("FeiShuDocumentService createDocument, folderToker:{}, title:{}", documentEntity.getFolderToken(), documentEntity.getTitle());
        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();

        // 创建请求对象
        CreateDocumentReq req = CreateDocumentReq.newBuilder()
                .createDocumentReqBody(CreateDocumentReqBody.newBuilder()
                        .folderToken(documentEntity.getFolderToken())
                        .title(documentEntity.getTitle())
                        .build())
                .build();

        // 发起请求
        CreateDocumentResp resp = client.docx().v1().document().create(req);

        // 处理服务端错误
        if(!resp.success()) {
            log.info("createDocument code:{},msg:{},reqId:{}, resp:{}",
                    resp.getCode(), resp.getMsg(), resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
            return new DocumentResult(ResultCodeEnum.FAIL);
        }
        // 业务数据处理
        log.info(gson.toJson(resp.getData()));
        // 使用 JsonParser 解析 JSON 字符串
        JsonObject jsonObject = JsonParser.parseString(gson.toJson(resp.getData())).getAsJsonObject();
        // 获取 document 字段的 JsonObject
        JsonObject dataObject = jsonObject.getAsJsonObject("document");
        // 获取 documentId 字段的值
        String documentId = dataObject.get("document_id").getAsString();
        DocumentResult result = new DocumentResult(ResultCodeEnum.SUCCESS);
        result.setDocumentId(documentId);
        result.setUrl(this.getDocUrl()+documentId);

        // 更新内容
        documentEntity.setDocumentId(documentId);
        documentEntity.setBlockId(documentId);
        if(!this.updateDocument(documentEntity)){
            result.setCode(ResultCodeEnum.FAIL);
        }
        return result;
    }
    public String getDocumentContent(String documentId, boolean isWiki) throws Exception {
        // 读取文档内容
        DocumentResult documentResult = this.readDocumentContent(documentId, isWiki);
        //处理读取结果
        if(documentResult!=null && ResultCodeEnum.SUCCESS.equals(documentResult.getCode())){
            return documentResult.getContent();
        }else{
            return null;
        }
    }
    public DocumentResult readDocumentContent(String documentId,boolean isWiki) throws Exception {
        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();
        if(isWiki){
            // Step 2: 获取知识库节点信息，获取 obj_token
            GetNodeSpaceResp nodeResp = client.wiki().space().getNode(GetNodeSpaceReq.newBuilder()
                    .token(documentId) // 替换为知识库节点的 token
                    .build());
            // 检查接口调用是否成功
            if (!nodeResp.success()) {
                log.info(String.format("Error: code=%s, msg=%s, reqId=%s",
                        nodeResp.getCode(), nodeResp.getMsg(), nodeResp.getRequestId()));
                return new DocumentResult(ResultCodeEnum.FAIL);
            }
            // 获取文档的 obj_token 和类型
            String objToken = nodeResp.getData().getNode().getObjToken();
            String objType = nodeResp.getData().getNode().getObjType();
            log.info("objToken:{},objType:{}", objToken, objType);
            documentId = objToken;
        }

        // 创建请求对象
        RawContentDocumentReq req = RawContentDocumentReq.newBuilder()
                .documentId(documentId)
                .lang(0)
                .build();

        // 发起请求
        RawContentDocumentResp resp = client.docx().v1().document().rawContent(req);

        // 处理服务端错误
        if(!resp.success()) {
            log.info("code:{},msg:{},reqId:{}, resp:{}",
                    resp.getCode(), resp.getMsg(), resp.getRequestId(),new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
            return new DocumentResult(ResultCodeEnum.FAIL);
        }

        // 业务数据处理
        log.info(gson.toJson(resp.getData()));
        // 使用 JsonParser 解析 JSON 字符串
        JsonObject jsonObject = JsonParser.parseString(gson.toJson(resp.getData())).getAsJsonObject();
        // 获取 documentId 字段的值
        String content = jsonObject.get("content").getAsString();
        DocumentResult documentResult = new DocumentResult(ResultCodeEnum.SUCCESS);
        documentResult.setContent(content);
        return documentResult;
    }
    public DocumentResult readDocumentContent(String documentId) throws Exception {
        return this.readDocumentContent(documentId,false);
    }
    private boolean updateDocument(DocumentEntity documentEntity) throws Exception {
        log.info("FeiShuDocumentService updateDocument, documentId:{}, blockId:{}, content:{}}", documentEntity.getDocumentId(),
                documentEntity.getBlockId(), documentEntity.getContent());
        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();

        // 创建请求对象
        CreateDocumentBlockChildrenReq req = CreateDocumentBlockChildrenReq.newBuilder()
                .documentId(documentEntity.getDocumentId())
                .blockId(documentEntity.getBlockId())
                .documentRevisionId(-1)
                .createDocumentBlockChildrenReqBody(CreateDocumentBlockChildrenReqBody.newBuilder()
                        .children(new Block[] {
                                Block.newBuilder()
                                        .blockType(2)
                                        .text(Text.newBuilder()
                                                .style(TextStyle.newBuilder()
                                                        .build())
                                                .elements(new TextElement[] {
                                                        TextElement.newBuilder()
                                                                .textRun(TextRun.newBuilder()
                                                                        .content(documentEntity.getContent())
                                                                        .build())
                                                                .build()
                                                })
                                                .build())
                                        .build()
                        })
                        .index(0)
                        .build())
                .build();

        // 发起请求
        CreateDocumentBlockChildrenResp resp = client.docx().v1().documentBlockChildren().create(req);

        // 处理服务端错误
        if(!resp.success()) {
            log.info("updateDocument code:{},msg:{},reqId:{}, resp:{}",
                    resp.getCode(), resp.getMsg(), resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
            return false;
        }
        log.info(gson.toJson(resp.getData()));
        // 业务数据处理
        return true;
    }
    public DocumentInfoResult getDocumentInfo(DocumentInfoEntity documentInfoEntity) throws Exception {
        if (StringUtils.isBlank(documentInfoEntity.getDocumentId())) {
            log.error("getDocumentInfo param error");
            return new DocumentInfoResult(ResultCodeEnum.FAIL);
        }

        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();

        // 创建请求对象
        GetDocumentReq req = GetDocumentReq.newBuilder()
                .documentId(documentInfoEntity.getDocumentId())
                .build();

        // 发起请求
        GetDocumentResp resp = client.docx().v1().document().get(req);

        // 处理服务端错误
        if(!resp.success()) {
            log.info("getDocumentInfo code:{},msg:{},reqId:{}, resp:{}",
                    resp.getCode(), resp.getMsg(), resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
            return new DocumentInfoResult(ResultCodeEnum.FAIL);
        }

        // 业务数据处理
        log.info(gson.toJson(resp.getData()));
        // 使用 JsonParser 解析 JSON 字符串
        JsonObject jsonObject = JsonParser.parseString(gson.toJson(resp.getData())).getAsJsonObject();
        // 获取 document 字段的 JsonObject
        JsonObject dataObject = jsonObject.getAsJsonObject("document");
        // 获取 title 字段的值
        String title = dataObject.get("title").getAsString();
        
        DocumentInfoResult result = new DocumentInfoResult(ResultCodeEnum.SUCCESS);
        DocumentInfoResult.DocumentInfoData data = new DocumentInfoResult.DocumentInfoData();
        data.setTitle(title);
        result.setData(data);
        return result;
    }
}
