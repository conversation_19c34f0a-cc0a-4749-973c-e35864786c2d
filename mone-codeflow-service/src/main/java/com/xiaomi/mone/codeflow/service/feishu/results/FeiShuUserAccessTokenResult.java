package com.xiaomi.mone.codeflow.service.feishu.results;

import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FeiShuUserAccessTokenResult extends BaseResult {
    // API响应相关字段
    private int apiCode;
    private String msg;

    // 用户访问令牌相关字段
    private String accessToken;
    private String refreshToken;
    private String tokenType;
    private Integer expiresIn;
    private Integer refreshExpiresIn;
    private String scope;

    public FeiShuUserAccessTokenResult() {
        super(ResultCodeEnum.SUCCESS);
    }

    public FeiShuUserAccessTokenResult(ResultCodeEnum code) {
        super(code);
    }
} 