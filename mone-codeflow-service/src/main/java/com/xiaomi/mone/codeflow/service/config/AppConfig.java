package com.xiaomi.mone.codeflow.service.config;

import java.time.Duration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;

@Configuration
@Data
public class AppConfig {

    @NacosValue("${llm.base-url}")
    private String baseUrl;

    @NacosValue("${llm.api-key}")
    private String apiKey;

    @NacosValue("${llm.api-model}")
    private String modelName;

    @NacosValue("${llm.temperature:0.7}")
    private double temperature;

    @NacosValue("${llm.top-p:1}")
    private double topP;

    @NacosValue("${llm.timeout:30}")
    private int timeout;

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册Java 8日期时间模块
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 配置LocalDateTime序列化器，使用指定的日期时间格式
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));

        mapper.registerModule(javaTimeModule);

        // 禁用将日期写为时间戳的功能
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        return mapper;
    }

    @Bean
    public ChatLanguageModel chatLanguageModel() {
        try {
            return OpenAiChatModel.builder()
                    .baseUrl(baseUrl)
                    .apiKey(apiKey)
                    .modelName(modelName)
                    .temperature(temperature)
                    .topP(topP)
                    .timeout(Duration.ofSeconds(timeout))
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("创建ChatLanguageModel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建指定最大令牌数的ChatLanguageModel
     *
     * @param maxTokens 最大令牌数
     * @return 配置了最大令牌数的ChatLanguageModel实例
     */
    public ChatLanguageModel createChatModelWithMaxTokens(int maxTokens) {
        try {
            return OpenAiChatModel.builder()
                    .baseUrl(baseUrl)
                    .apiKey(apiKey)
                    .modelName(modelName)
                    .temperature(temperature)
                    .maxTokens(maxTokens)
                    .timeout(Duration.ofSeconds(timeout))
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("创建ChatLanguageModel失败: " + e.getMessage(), e);
        }
    }
}