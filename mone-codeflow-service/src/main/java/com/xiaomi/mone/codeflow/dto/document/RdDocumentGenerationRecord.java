package com.xiaomi.mone.codeflow.dto.document;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 技术文档生成记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RdDocumentGenerationRecord {

    /**
     * 自增ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private String uid;

    /**
     * Meego需求URL
     */
    private String meegoUrl;
    
    /**
     * 飞书文档URL
     */
    private String feishuUrl;
    
    /**
     * 飞书文档URL
     */
    private String rddocUrl;
    
    /**
     * PRD内容
     */
    private String prdContent;
    
    /**
     * API文档内容
     */
    private String apiDocContent;
    
    /**
     * SQL文档内容
     */
    private String sqlDocContent;
    
    /**
     * 知识库导入信息，JSON字符串
     */
    private String knowledge;
    
    /**
     * 生成的结果内容
     */
    private String resultContent;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
} 