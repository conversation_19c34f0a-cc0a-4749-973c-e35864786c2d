package com.xiaomi.mone.codeflow.service.feishu;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.gson.Gson;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

@Data
public class FeiShuBaseService {
    @NacosValue("${feishu.api.appId:}")
    private String appId;

    @NacosValue("${feishu.api.appSecret:}")
    private String appSecret;

    @NacosValue("${feishu.api.openBaseUrl:}")
    private String openBaseUrl;

    @NacosValue("${feishu.doc.url:}")
    private String docUrl;

    @NacosValue("${feishu.file.url:}")
    private String fileUrl;

    @NacosValue("${feishu.folder.url:}")
    private String folderUrl;

    @NacosValue("${feishu.api.defaultFolderToken:}")
    private String defaultFolderToken;

    @NacosValue("${feishu.api.uploadFolderToken:}")
    private String uploadFolderToken;

    @NacosValue("${feishu.get.doc.times:3}")
    private int getDocTimes;

    @NacosValue("${feishu.get.doc.interval:500}")
    private int getDocInterval;

    protected  Gson gson = new Gson();
}
