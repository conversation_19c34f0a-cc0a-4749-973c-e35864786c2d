package com.xiaomi.mone.codeflow.service.feishu;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import com.xiaomi.mone.codeflow.service.feishu.results.RootFolderMetaResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class FeiShuSpaceService extends FeiShuBaseService {
    
    private final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build();

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public RootFolderMetaResult getRootFolderMeta(String userAccessToken) {
        try {
            String url = this.getOpenBaseUrl() + "/open-apis/drive/explorer/v2/root_folder/meta";
            log.info("Requesting root folder meta from URL: {}", url);
            
            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + userAccessToken)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to get root folder meta. Response code: {}, Response body: {}", 
                        response.code(), response.body() != null ? response.body().string() : "null");
                    return new RootFolderMetaResult(ResultCodeEnum.FAIL);
                }

                String responseBody = response.body().string();
                log.info("Root folder meta response: {}", responseBody);
                
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                if (jsonNode.get("code").asInt() != 0) {
                    log.error("Failed to get root folder meta: {}", jsonNode.get("msg").asText());
                    return new RootFolderMetaResult(ResultCodeEnum.FAIL);
                }

                RootFolderMetaResult result = new RootFolderMetaResult(ResultCodeEnum.SUCCESS);
                RootFolderMetaResult.RootFolderMetaData data = new RootFolderMetaResult.RootFolderMetaData();
                JsonNode dataNode = jsonNode.get("data");
                data.setToken(dataNode.get("token").asText());
                data.setId(dataNode.get("id").asText());
                data.setUser_id(dataNode.get("user_id").asText());
                result.setData(data);
                
                return result;
            }
        } catch (Exception e) {
            log.error("Error getting root folder meta: {}", e.getMessage(), e);
            return new RootFolderMetaResult(ResultCodeEnum.FAIL);
        }
    }
}
