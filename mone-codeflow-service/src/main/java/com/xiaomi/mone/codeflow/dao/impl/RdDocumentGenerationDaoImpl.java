package com.xiaomi.mone.codeflow.dao.impl;

import com.xiaomi.mone.codeflow.dao.RdDocumentGenerationDao;
import com.xiaomi.mone.codeflow.dto.document.RdDocumentGenerationRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 技术文档生成记录DAO实现类
 */
@Slf4j
@Repository
public class RdDocumentGenerationDaoImpl implements RdDocumentGenerationDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public int save(RdDocumentGenerationRecord record) {
        String sql = "INSERT INTO rd_document_generation (uid, feishu_url, rddoc_url, prd_content, api_doc_content, " +
                "sql_doc_content, result_content, meego_url, knowledge) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        KeyHolder keyHolder = new GeneratedKeyHolder();
        
        int result = jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            ps.setString(1, record.getUid());
            ps.setString(2, record.getFeishuUrl());
            ps.setString(3, record.getRddocUrl());
            ps.setString(4, record.getPrdContent());
            ps.setString(5, record.getApiDocContent());
            ps.setString(6, record.getSqlDocContent());
            ps.setString(7, record.getResultContent());
            ps.setString(8, record.getMeegoUrl());
            ps.setString(9, record.getKnowledge());
            return ps;
        }, keyHolder);
        
        if (result > 0 && keyHolder.getKey() != null) {
            record.setId(keyHolder.getKey().longValue());
        }
        
        return result;
    }

    @Override
    public RdDocumentGenerationRecord findById(Long id) {
        try {
            String sql = "SELECT * FROM rd_document_generation WHERE id = ?";
            return jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(RdDocumentGenerationRecord.class), id);
        } catch (Exception e) {
            log.error("查询文档生成记录失败, id: {}", id, e);
            return null;
        }
    }

    @Override
    public List<RdDocumentGenerationRecord> findByUid(String uid) {
        String sql = "SELECT * FROM rd_document_generation WHERE uid = ? ORDER BY create_time DESC";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(RdDocumentGenerationRecord.class), uid);
    }
    
    @Override
    public List<RdDocumentGenerationRecord> findByUidWithPage(String uid, int offset, int limit) {
        String sql = "SELECT * FROM rd_document_generation WHERE uid = ? ORDER BY create_time DESC LIMIT ?, ?";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(RdDocumentGenerationRecord.class), uid, offset, limit);
    }
    
    @Override
    public long countByUid(String uid) {
        String sql = "SELECT COUNT(*) FROM rd_document_generation WHERE uid = ?";
        return jdbcTemplate.queryForObject(sql, Long.class, uid);
    }
    
    @Override
    public int update(RdDocumentGenerationRecord record) {
        String sql = "UPDATE rd_document_generation SET feishu_url = ?, rddoc_url = ?, prd_content = ?, api_doc_content = ?, " +
                "sql_doc_content = ?, result_content = ?, meego_url = ? WHERE id = ?";
        
        return jdbcTemplate.update(sql,
                record.getFeishuUrl(),
                record.getRddocUrl(),
                record.getPrdContent(),
                record.getApiDocContent(),
                record.getSqlDocContent(),
                record.getResultContent(),
                record.getMeegoUrl(),
                record.getId());
    }
    
    @Override
    public int delete(Long id) {
        String sql = "DELETE FROM rd_document_generation WHERE id = ?";
        return jdbcTemplate.update(sql, id);
    }
} 