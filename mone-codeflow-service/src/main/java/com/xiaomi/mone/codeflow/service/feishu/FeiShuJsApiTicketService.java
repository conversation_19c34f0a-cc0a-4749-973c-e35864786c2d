package com.xiaomi.mone.codeflow.service.feishu;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import com.xiaomi.mone.codeflow.service.feishu.FeiShuBaseService;

@Slf4j
@Service
public class FeiShuJsApiTicketService extends FeiShuBaseService {

    @Resource
    private FeiShuAppAccessTokenService feiShuAppAccessTokenService;

    private final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build();

    private final ObjectMapper objectMapper = new ObjectMapper();

    public String getJsApiTicket(String userAccessToken) {
        try {
            String ticket = getTicket(userAccessToken);
            if (ticket == null) {
                log.error("Failed to get jsapi ticket with user_access_token: {}", userAccessToken);
            } else {
                log.info("Successfully got jsapi ticket");
            }
            return ticket;
        } catch (Exception e) {
            log.error("Error getting jsapi ticket: {}", e.getMessage(), e);
            return null;
        }
    }

    private String getTicket(String userAccessToken) throws IOException {
        String url = this.getOpenBaseUrl() + "/open-apis/jssdk/ticket/get";
        log.info("Requesting jsapi ticket from URL: {}", url);
        
        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", "Bearer " + userAccessToken)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to get jsapi ticket. Response code: {}, Response body: {}", 
                    response.code(), response.body() != null ? response.body().string() : "null");
                return null;
            }

            String responseBody = response.body().string();
            log.info("Jsapi ticket response: {}", responseBody);
            
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            if (jsonNode.get("code").asInt() != 0) {
                log.error("Failed to get jsapi ticket: {}", jsonNode.get("msg").asText());
                return null;
            }

            return jsonNode.get("data").get("ticket").asText();
        }
    }
}
