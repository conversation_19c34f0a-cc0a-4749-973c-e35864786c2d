package com.xiaomi.mone.codeflow.service.feishu;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.drive.v1.model.*;
import com.lark.oapi.core.request.RequestOptions;
import com.xiaomi.mone.codeflow.common.enums.FileTypeEnum;
import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import com.xiaomi.mone.codeflow.service.feishu.entitys.DocumentEntity;
import com.xiaomi.mone.codeflow.service.feishu.results.DocumentResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class FeiShuUploadFileService extends FeiShuBaseService{
    public DocumentResult uploadFile(DocumentEntity documentEntity) throws Exception {
        if (StringUtils.isBlank(documentEntity.getContent())|| documentEntity.getFileType() == null){
            log.error("uploadFile param error");
            return new DocumentResult( ResultCodeEnum.FAIL );
        }
        // 生成临时文件
        File file = createTempFile(documentEntity.getContent(), documentEntity.getTitle(), documentEntity.getFileType().getFullType());
        return uploadFile(documentEntity,file);
    }
    public DocumentResult uploadFile(DocumentEntity documentEntity,File file) throws Exception {

        // 设置默认文件夹
        if(StringUtils.isBlank(documentEntity.getFolderToken())){
            documentEntity.setFolderToken(this.getDefaultFolderToken());
        }

        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();

        // 创建请求对象，如果是word文档，则上传到默认文件夹，否则上传到指定文件夹
        UploadAllFileReq req = UploadAllFileReq.newBuilder()
                .uploadAllFileReqBody(UploadAllFileReqBody.newBuilder()
                        .fileName(documentEntity.getTitle() + documentEntity.getFileType().getFullType())
                        .parentType("explorer")
                        .parentNode(documentEntity.getFolderToken())
                        .size((int)file.length())
                        .file(file)
                        .build())
                .build();

        // 发起请求
        UploadAllFileResp resp;
        if (StringUtils.isNotBlank(documentEntity.getUserAccessToken())) {
            resp = client.drive().v1().file().uploadAll(
                req,
                RequestOptions.newBuilder().userAccessToken(documentEntity.getUserAccessToken()).build()
            );
        } else {
            resp = client.drive().v1().file().uploadAll(req);
        }

        // 处理服务端错误
        if(!resp.success()) {
            log.error("code:{},msg:{},reqId:{}, resp:{}",
                    resp.getCode(), resp.getMsg(), resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
            return new DocumentResult(ResultCodeEnum.FAIL);
        }

        // 业务数据处理
        log.info(gson.toJson(resp.getData()));
        // 使用 JsonParser 解析 JSON 字符串
        JsonObject jsonObject = JsonParser.parseString(gson.toJson(resp.getData())).getAsJsonObject();
        // 获取 file_token 字段的值
        String fileToken = jsonObject.get("file_token").getAsString();
        // 兜底返回上传文档，可以打开后手动生成飞书文档
        DocumentResult documentResult = new DocumentResult(ResultCodeEnum.SUCCESS);
        documentResult.setDocumentId(fileToken);
        documentResult.setUrl(this.getFileUrl() + fileToken);
        documentResult.setFileType(documentEntity.getFileType());
        // 自动生成飞书文档
        String ticket = this.importTask(documentEntity, fileToken);
        // 因为文档生成需要时间，所以需要循环获取，如果超过次数，则返回失败
        int getDocTimes = this.getGetDocTimes();
        while (getDocTimes > 0) {
            getDocTimes--;
            Thread.sleep(this.getGetDocInterval());
            String url = this.getImportTesk(ticket, documentEntity.getUserAccessToken());
            if (StringUtils.isNotBlank(url)) {
                documentResult.setDocumentId(url.substring(url.lastIndexOf("/") + 1));
                documentResult.setUrl(url);
                documentResult.setFileType(FileTypeEnum.DOCX);
                break;
            }
        }
        return documentResult;
    }
    public String getDocxByTicketId(String ticketId, String userAccessToken) throws Exception {
        return this.getImportTesk(ticketId, userAccessToken);
    }

    private String  importTask(DocumentEntity documentEntity, String fileToken) throws Exception {
        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();

        // 创建请求对象
        CreateImportTaskReq req = CreateImportTaskReq.newBuilder()
                .importTask(ImportTask.newBuilder()
                        .fileExtension(documentEntity.getFileType().getType())
                        .fileToken(fileToken)
                        .type(FileTypeEnum.DOCX.getType())
                        .fileName(documentEntity.getTitle())
                        .point(ImportTaskMountPoint.newBuilder()
                                .mountType(1)
                                .mountKey(documentEntity.getFolderToken())
                                .build())
                        .build())
                .build();

        // 发起请求
        CreateImportTaskResp resp;
        if (StringUtils.isNotBlank(documentEntity.getUserAccessToken())) {
            resp = client.drive().v1().importTask().create(
                req,
                RequestOptions.newBuilder().userAccessToken(documentEntity.getUserAccessToken()).build()
            );
        } else {
            resp = client.drive().v1().importTask().create(req);
        }

        // 处理服务端错误
        if(!resp.success()) {
            log.error("code:{},msg:{},reqId:{}, resp:{}",
                    resp.getCode(), resp.getMsg(), resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
            return null;
        }

        // 业务数据处理
        log.info(gson.toJson(resp.getData()));
        // 使用 JsonParser 解析 JSON 字符串
        JsonObject jsonObject = JsonParser.parseString(gson.toJson(resp.getData())).getAsJsonObject();
        // 获取 ticket 字段的值
        return jsonObject.get("ticket").getAsString();
    }

    private String getImportTesk(String ticket, String userAccessToken) throws Exception {
        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();

        // 创建请求对象
        GetImportTaskReq req = GetImportTaskReq.newBuilder()
                .ticket(ticket)
                .build();

        // 发起请求
        GetImportTaskResp resp;
        if (StringUtils.isNotBlank(userAccessToken)) {
            resp = client.drive().v1().importTask().get(
                req,
                RequestOptions.newBuilder().userAccessToken(userAccessToken).build()
            );
        } else {
            resp = client.drive().v1().importTask().get(req);
        }

        // 处理服务端错误
        if(!resp.success()) {
            log.error("code:{},msg:{},reqId:{}, resp:{}",
                    resp.getCode(), resp.getMsg(), resp.getRequestId(), new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8));
            return null;
        }

        // 业务数据处理
        log.info(gson.toJson(resp.getData()));
        // 使用 JsonParser 解析 JSON 字符串
        JsonObject jsonObject = JsonParser.parseString(gson.toJson(resp.getData())).getAsJsonObject();
        // 获取 document 字段的 JsonObject
        JsonObject dataObject = jsonObject.getAsJsonObject("result");
        String jobStatus =dataObject.get("job_status").getAsString();
        if("0".equals(jobStatus)){
            // 获取 url 字段的值
            return dataObject.get("url").getAsString();
        }
        return null;
    }

    private File createTempFile(String content, String fileName, String fileType) throws IOException {
        // 创建临时文件
        File tempFile = File.createTempFile(fileName, fileType);
        tempFile.deleteOnExit(); // 确保临时文件在程序退出时删除

        // 写入内容到临时文件
        try (FileWriter writer = new FileWriter(tempFile)) {
            writer.write(content);
        }

        return tempFile;
    }
}
