package com.xiaomi.mone.codeflow.service.template;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;

import org.springframework.stereotype.Service;

import freemarker.template.Configuration;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import com.alibaba.nacos.api.config.annotation.NacosValue;

@Slf4j
@Service
public class RDTemplateManager {

    @Value("${llm.max-tokens}")
    private int maxTokens;

    @NacosValue(value = "${llm.output-language:Simple Chinese}", autoRefreshed = true)
    private String outputLanguage;

    // 技术栈配置
    @NacosValue(value = "${prompt.tech_stack}", autoRefreshed = true)
    private String techStack;

    // 技术术语
    @NacosValue(value = "${core-terms-definition.json:$.terms}", autoRefreshed = true)
    private String techTerms;

    // 技术术语
    @NacosValue(value = "${high-risk-elements.json:$.rules}", autoRefreshed = true)
    private static String highRiskRules;

    private final Configuration freemarkerConfig;

    // 章节配置
    private static final Map<String, SectionConfig> SECTION_CONFIGS;

    // 章节模板映射
    private static final Map<String, String> SECTION_TEMPLATES;

    // 章节特定知识库
    private static final Map<String, String> SECTION_KNOWLEDGE_BASES;

    // 章节特定规则
    private static final Map<String, String> SECTION_RULES;

    // 章节块输出顺序
    public static final List<String> finalOrderedSections = Arrays.asList(
            "design_summary",           // 1. 设计概要
            "architecture_design",      // 2. 架构设计/解决方案设计
            "interface_design",         // 3. 接口设计
            "external_dependencies",    // 4. 外部依赖
            "internal_design",          // 5. 内部设计
            "data_design",             // 6. 数据设计
            "deployment_ops",          // 7. 部署/运维
            "high_risk_elements",      // 8. 高危元素
            "impact_range",            // 9. 影响范围
            "observability",           // 10. 可观测性
            "risk_assessment"          // 11. 风险评估
    );

    // 额外的模板内容
    private static final String EXTRA_TEMPLATE_CONTENT = 
        "\n## 12. 时间线\n" +
        "> 描述重要的时间点 （Meego 后续支持直接将排期链接到飞书文档）\n\n" +
        "## 13. 需求决策\n" +
        "> 本方案中如遇需决策的内容/事项，请遵照模板实施：[MIT 技术方案决策模版 V1.0](https://xiaomi.f.mioffice.cn/wiki/KMk9wOYjaiAgfak0XxTkqxcZ4Fc) 请创建副本使用\n" +
        "> 模板中涵盖需决策的原因、内容、影响范围、相关组织/人员、评判标准、候选方案的详情和比较结果、最终决策结果等；\n\n" +
        "## 14. 其他参考文档\n" +
        "- 需求分析：\n" +
        "- 产品设计：\n" +
        "- UI 设计：\n" +
        "- 其他详细设计：\n" +
        "- 测试方案设计：\n\n" +
        "## 15. 评审记录\n\n" +
        "| 评审成员 | 评审日期 | 评审结论 |\n" +
        "|---|---|---|\n" +
        "| | | |\n\n" +
        "| 章节 | 评审内容 | 评审意见 | 主要问题 | \n" +
        "|---|---|---|---|\n" +
        "| 业务相关 | 确保业务系统上下文没有遗漏 | | |\n" +
        "| 设计目标/考虑 | 评审非功能特性、风险评估部分是否有遗漏 | | |\n" +
        "| 外部依赖（使用外部的） | 评审外部依赖模块是否有遗漏 | | |\n" +
        "| 影响范围 | 评审影响范围是否正确评估，是否同步修改、发布 | | |\n" +
        "| 可观测性 | 评审重要业务是否考虑可观测性 | | |\n" +
        "| 风险评估 | 评估当前方案所基于的假设是否可靠，应对措施是否可行 | | |\n" +
        "| 高危元素 | 高危元素的识别&自检内容完整且准确，检查结论组内达成一致 | | |\n" +
        "| | | | |\n";

    // 术语提取Prompt模板
    public static final String TERM_EXTRACTION_PROMPT_TEMPLATE =
        "请从以下 PRD 内容中提取对\"技术方案设计\"阶段有影响的关键技术元素：\n" +
        "- 系统名称和类型\n" +
        "- 软件组件和模块\n" +
        "- 中间件和基础设施\n" +
        "- 服务和API\n" +
        "- 数据库和存储系统\n" +
        "- 通信协议和接口\n" +
        "- 技术标准和规范\n\n" +

        "只关注技术元素，不需要分析具体功能点。对于每个识别的元素，请生成：\n" +
        "- 技术元素名称\n" +
        "- 元素类型（系统/组件/中间件/服务等）\n" +
        "- 不超过 3 个对技术方案设计有价值的问题（避免常识性问题）\n\n" +

        "请输出 JSON 格式，结构如下：\n" +
        "[\n" +
        "  {\n" +
        "    \"term\": \"<技术元素>\",\n" +
        "    \"keywords\": [\"<技术元素类型>\"],\n" +
        "    \"questions\": [\"...\", \"...\", \"...\"]\n" +
        "  },\n" +
        "  ...\n" +
        "]\n\n" +
        "以下是产品需求文档内容，请开始处理：\n{prdContent}\n";


    static {
        // 初始化章节配置
        Map<String, SectionConfig> configMap = new HashMap<>();
        Map<String, String> templateMap = new HashMap<>();
        Map<String, String> knowledgeBaseMap = new HashMap<>();
        Map<String, String> rulesMap = new HashMap<>();

        // 初始化章节配置
        configMap.put("design_summary", new SectionConfig(3000, Collections.emptyList()));
        configMap.put("architecture_design", new SectionConfig(3000, Arrays.asList("design_summary")));
        configMap.put("interface_design", new SectionConfig(3000, Arrays.asList("architecture_design")));
        configMap.put("external_dependencies", new SectionConfig(2000, Arrays.asList("architecture_design")));
        configMap.put("internal_design", new SectionConfig(3000, Arrays.asList("design_summary", "architecture_design")));
        configMap.put("data_design", new SectionConfig(3000, Arrays.asList("architecture_design")));
        configMap.put("deployment_ops", new SectionConfig(2000, Arrays.asList("data_design")));
        configMap.put("high_risk_elements", new SectionConfig(2000, Arrays.asList("architecture_design", "data_design")));
        configMap.put("impact_range", new SectionConfig(2000, Arrays.asList("architecture_design", "data_design")));
        configMap.put("observability", new SectionConfig(2000, Arrays.asList("architecture_design", "data_design")));
        configMap.put("risk_assessment", new SectionConfig(2000, Arrays.asList("high_risk_elements", "impact_range")));

        // 初始化章节模板
        templateMap.put("design_summary",
            "# {PRD文档中的项目名称}\n\n" +
            "## 1. 设计概要\n\n" +
            "### 1.1 背景\n\n" +
            "<hide>请简要介绍系统相关信息（这个系统是干什么的？上下游存在哪些依赖？）、需求来源（谁想解决什么问题 或 提升什么？），请与 PRD 内容保持一致</hide>\n\n" +
            "- 系统简介：\n" +
            "    - 上游依赖：\n" +
            "    - 下游依赖：\n" +
            "- 需求来源：\n" +
            "- 参考材料：\n" +
            "    - 产品方案：<hide>示例：[PRD项目名称](prd_url)</hide>\n\n" +
            "### 1.2 设计范围\n\n" +
            "<hide>建议使用Mermaid图文/图表形式，将业务流程按模块拆分并分组，确定设计的边界（重点围绕哪些程序、模块或子程序展开设计）</hide>\n\n" +
            "- 功能特性\n\n" +
            "<hide>请将业务流程按模块拆分并分组，清晰阐述功能分类并配备说明</hide>\n" +
            "| 编号 | 功能模块 | 一级功能 | 二级功能 | 三级功能 | 说明 |\n" +
            "|---|---|---|---|---|---|\n" +
            "| 1 | | | | | |\n\n" +
            "- 非功能特性：\n" +
            "<hide>系统的非功能特性也是系统的重要组成部分。包括但不限于（性能、安全、可测试性、体验等多个方面）</hide>\n" +
            "<hide>请从以下维度逐个完成分析，若不涉及填写\"不涉及\"即可（请勿删除）</hide>\n" +
            "    - 性能/响应时间/吞吐:\n" +
            "    - 安全/隐私:\n" +
            "    - 易用性：\n" +
            "    - 可测试性：\n\n" +
            "### 1.3 设计难点\n\n" +
            "<hide>简要说明需求通过技术手段实现的过程中，可能会存在的限制或阻碍点，如技术债、关联系统依赖、权限/安全风险等</hide>\n\n" +
            "<hide>❗注意：说明难点的同时，请说明计划应对难点的措施，或说明需要什么支持</hide>\n\n" +
            "### 1.4 术语定义\n\n" +
            "<hide>解释说明该文档中用到的领域名词的全称和定义。</hide>\n" +
            "<hide>领域名词。建立业务、产品、研发的一致语言。统一概念，统一认知</hide>\n\n" +
            "| 编号 | 名词 | 英文 | 释义 |\n" +
            "|---|---|---|---|\n" +
            "| 1 | | | |\n"
        );

        templateMap.put("architecture_design",
            "## 2. 架构设计/解决方案设计\n\n" +
            "### 2.1 整体解决方案\n\n" +
            "<hide>建议使用Mermaid图文形式，重点描述从整体、架构层面，如何解决问题</hide>\n\n" +
            "### 2.2 架构和关系图\n\n" +
            "<hide>描述系统间、模块间的关系、每个系统/模块的定位或职责边界，并且可以与业务模块映射</hide>\n\n" +
            "<hide>建议使用Mermaid模块关系图、时序图（依赖关系复杂）说明，ER 图、领域模型、系统层次图、部署结构图可作为补充说明</hide>\n\n" +
            "<hide>服务名称建议写全称+缩写，或缩写+全称释义</hide>\n\n" +
            "### 2.3 系统触发条件\n\n" +
            "<hide>描述该系统如何被外部使用和触发<hide>\n" +
            "- 前端交互触发\n" +
            "- 消息通知触发\n" +
            "- 定时任务触发\n" +
            "- 系统间调用触发\n"
        );

        templateMap.put("interface_design",
            "## 3. 接口设计（对外提供的）\n\n" +
            "<hide>接口设计通常描述的是，本系统对外提供的接口，接受外部的输入，涉及前后端接口的，接口设计优先考虑。</hide>\n\n" +
            "### 3.1 接口协议\n\n" +
            "<hide>HTTP (REST, WebSocket, GraphQL), RPC(gRPC, Thrift, Dubbo)</hide>\n" +
            "<hide>使用什么安全协议？</hide>\n" +
            "前后端交互，直接使用 MiAPI\n\n" +
            "- 新增接口\n\n" +
            "\n<hide>罗列实现PRD全部功能所需要新增的全部接口，不允许遗漏</hide>\n" +
            "| 接口名称 | 请求方法 | 接口路径 | 接口描述 | 请求参数 |\n" +
            "|---|---|---|---|---|\n" +
            "| 1 | | | | |\n\n" +
            "<hide>如果需要在已有的接口上变更，则展示修订接口，不允许遗漏\n" +
            "- 修订接口\n\n" +
            "| 接口名称 | 请求方法 | 接口路径 | 变更描述 | 请求参数 |\n" +
            "|---|---|---|---|---|\n" +
            "| 1 | | | | |\n" +
            "</hide>\n\n" +
            "### 3.2 接受的消息队列\n\n" +
            "<hide>\n" +
            "描述结构，队列方式、名称。如果有，则展示列表，否则显示\"无\"\n\n" +
            "| 队列名称 | 队列类型 | 说明 |\n" +
            "|---|---|---|\n" +
            "| 1 | | |\n" +
            "### 3.3 任务调度\n\n" +
            "<hide>\n" +
            "是否有定时任务，如何调度？还是外部触发？如果有，则展示类表，否则显示\"无\"\n" +
            "| 任务名称 | 触发方式 | 说明 |\n" +
            "|---|---|---|\n" +
            "| 1 | | |\n" +
            "</hide>\n\n"
        );

        templateMap.put("external_dependencies",
            "## 4. 外部依赖（使用外部的）\n\n" +
            "<hide>一般描述，将数据传输给下游</hide>\n" +
            "- 服务调用（rpc, http 或其他）\n" +
            "- Message queue\n\n" +
            "### 4.1 依赖的外部服务\n\n" +
            "<hide>使用了什么外部服务？</hide>\n\n" +
            "### 4.2 通过消息队列输出\n\n" +
            "<hide>将消息发往何处</hide>\n"
        );

        templateMap.put("internal_design",
            "## 5. 内部设计\n\n" +
            "<hide>" +
            "描述系统内部的设计，包括但不限于：\n\n" +
            "- 采用什么框架\n" +
            "- 有没有任务调度\n" +
            "- 类关系图，交互图、序列图是否有\n" +
            "- 技术选型\n" +
            "</hide>\n\n" +
            "### 5.1 领域分解\n\n" +

            "### 5.2 类图/交互图/序列图/流程图 等\n\n" +
            "<hide>使用Mermaid图文形式，描述系统内部关系</hide>\n\n" +
            "### 5.3 高可用设计\n\n" +
            "<hide>是否需要，以及使用什么形式？</hide>\n\n" +
            "### 5.4 非功能特性设计\n\n" +
            "<hide>如何满足1.2 中非功能特性？</hide>\n"
        );

        templateMap.put("data_design",
            "## 6. 数据设计\n\n" +
            "### 6.1 数据安全性\n\n" +
            "<hide>是否要考虑隐私、敏感数据，哪些字段？如何加密？</hide>\n\n" +
            "### 6.2 数据库和表设计\n\n" +
            "<hide>数据的灾备，冗余。是否多机房，数据如何同步？是否有数据库变更？</hide>\n" +
            "<hide>数据库需要输出Schema表格和DDL，包括字段名、字段描述\n\n" +
            "- 示例表\n\n" +
            "| 序号 | 字段名称 | 字段类型 | 长度 | 索引 | 备注 |\n" +
            "|---|---|---|---|---|---|\n" +
            "| 1 | id | bigint | 20 | 是 | ID |\n\n" +
            "```sql\n" +
            "/*\n" +
            "表名称：示例表\n" +
            "表描述：用于存储示例数据\n" +
            "*/\n" +
            "CREATE TABLE `example_table` (\n" +
            "  `id` bigint NOT NULL COMMENT 'ID',\n" +
            "  `field_name` varchar(255) NOT NULL COMMENT '字段描述',\n" +
            "  PRIMARY KEY (`id`)\n" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='示例表';\n" +
            "/*\n" +
            "表名称：示例表1\n" +
            "表描述：用于存储示例数据1\n" +
            "字段变更：新增字段field_name\n" +
            "*/\n" +
            "ALTER TABLE `example_table_1` ADD COLUMN `field_name` int NOT NULL COMMENT '字段描述' AFTER `id`;\n" +
            "```</hide>\n\n" +
            "### 6.3 缓存设计\n\n" +
            "<hide>是否有数据格式变更？如何处理缓存失效？</hide>\n\n" +
            "### 6.4 数据对接设计\n\n" +
            "<hide>数据采用什么模式对接？直接推送、通知拉取、生产消费等</hide>\n"
        );

        templateMap.put("deployment_ops",
            "## 7. 部署/运维\n\n" +
            "- 服务的容灾：\n" +
            "- 部署信息：<hide>什么国家等信息</hide>\n" +
            "- 部署要求：\n" +
            "- 灰度方案：\n" +
            "- 上线方案：<hide>是否有数据库变更？</hide>\n"
        );

        templateMap.put("high_risk_elements",
            "## 8. 高危元素\n\n" +
            "\n<hide>根据高危元素识别规则，识别系统中的高危点，使用Markdown表格输出，包括高危项、原因、解决方案</hide>\n"
        );

        templateMap.put("impact_range",
            "## 9. 影响范围\n\n" +
            "<hide>通过此章节确保方案影响范围可控，可以参考从如下方向考虑</hide>\n\n" +
            "- 方案对上游的要求\n" +
            "- 方案对下游的影响\n" +
            "- 方案对运维的额外要求\n" +
            "- 方案对升级的要求，异常回滚的要求\n"
        );

        templateMap.put("observability",
            "## 10. 可观测性\n\n" +
            "<hide>使用什么形式的监控\n" +
            "- 讲清楚应该监控哪些指标：业务指标、技术指标\n\n" +
            "- 讲清楚应该对哪些核心指标配置报警：邮件、飞书、电话</hide>\n\n" +
            "- 监控形式＆路径：\n\n" +
            "- 监控指标：\n\n" +
            "- 报警方式：\n"
        );

        templateMap.put("risk_assessment",
            "## 11. 风险评估\n\n" +
            "<hide>识别方案潜在的风险，并制定对应措施，以降低风险发生的可能性和影响程度。可能的风险来自</hide>\n\n" +
            "- 方案使用的技术栈成熟度\n" +
            "- 需求的不确定性\n" +
            "- 升级系统过程中旧数据的处理逻辑或者数据量\n" +
            "- 方案本身可能基于某种假设\n"
        );

        // 初始化章节知识库
        knowledgeBaseMap.put("design_summary", "");
        knowledgeBaseMap.put("architecture_design", "");
        knowledgeBaseMap.put("interface_design", "");
        knowledgeBaseMap.put("external_dependencies", "");
        knowledgeBaseMap.put("internal_design", "");
        knowledgeBaseMap.put("data_design", "");
        knowledgeBaseMap.put("deployment_ops", "");
        knowledgeBaseMap.put("high_risk_elements", "```json\n" + highRiskRules + "\n```");
        knowledgeBaseMap.put("impact_range", "");
        knowledgeBaseMap.put("observability", "");
        knowledgeBaseMap.put("risk_assessment", "");

        // 初始化章节规则
        rulesMap.put("design_summary", "");
        rulesMap.put("architecture_design", "");
        rulesMap.put("interface_design", "");
        rulesMap.put("external_dependencies", "");
        rulesMap.put("internal_design", "");
        rulesMap.put("data_design", "");
        rulesMap.put("deployment_ops", "");
        rulesMap.put("high_risk_elements", "");
        rulesMap.put("impact_range", "");
        rulesMap.put("observability", "");
        rulesMap.put("risk_assessment", "");

        // 使配置不可变
        SECTION_CONFIGS = Collections.unmodifiableMap(configMap);
        SECTION_TEMPLATES = Collections.unmodifiableMap(templateMap);
        SECTION_KNOWLEDGE_BASES = Collections.unmodifiableMap(knowledgeBaseMap);
        SECTION_RULES = Collections.unmodifiableMap(rulesMap);
    }

    public RDTemplateManager() {
        freemarkerConfig = new Configuration(Configuration.VERSION_2_3_31);
        freemarkerConfig.setClassLoaderForTemplateLoading(this.getClass().getClassLoader(), "templates");
        freemarkerConfig.setDefaultEncoding("UTF-8");
    }

    /**
     * 生成章节提示词列表
     * @param data 输入数据
     * @return 章节提示词列表
     * @throws IOException 如果生成过程中出现IO错误
     */
    public List<SectionPrompt> generateSectionPrompts(Map<String, Object> data) throws IOException {
        // 获取基于依赖关系的章节顺序
        List<String> orderedSections = getOrderedSections();

        // 生成每个章节的prompt
        List<SectionPrompt> sectionPrompts = new ArrayList<>();
        Map<String, String> generatedContent = new HashMap<>();

        for (String sectionName : orderedSections) {
            SectionConfig config = SECTION_CONFIGS.get(sectionName);
            if (config == null) {
                continue;
            }

            // 准备章节上下文
            Map<String, Object> sectionContext = prepareSectionContext(sectionName, data, generatedContent);

            // 生成章节prompt
            String sectionPrompt = generateSectionPrompt(sectionName, sectionContext);

            // 组装prompt
            sectionPrompts.add(new SectionPrompt(sectionName, sectionPrompt, this.maxTokens));
        }

        return sectionPrompts;
    }

    /**
     * 获取基于依赖关系排序的章节列表
     * @return 排序后的章节列表
     */
    private List<String> getOrderedSections() {
        // 使用Kahn的拓扑排序算法
        Map<String, List<String>> graph = new HashMap<>();
        Map<String, Integer> inDegree = new HashMap<>();

        // 初始化图和入度
        for (Map.Entry<String, SectionConfig> entry : SECTION_CONFIGS.entrySet()) {
            String section = entry.getKey();
            List<String> dependencies = entry.getValue().getDependencies();

            // 确保每个节点都在图中
            graph.putIfAbsent(section, new ArrayList<>());
            inDegree.putIfAbsent(section, 0);

            // 处理依赖关系
            for (String dependency : dependencies) {
                graph.putIfAbsent(dependency, new ArrayList<>());
                graph.get(dependency).add(section);
                inDegree.put(section, inDegree.getOrDefault(section, 0) + 1);
            }
        }

        // 拓扑排序
        List<String> result = new ArrayList<>();
        Queue<String> queue = new LinkedList<>();

        // 加入所有入度为0的节点
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.add(entry.getKey());
            }
        }

        while (!queue.isEmpty()) {
            String current = queue.poll();
            result.add(current);

            for (String neighbor : graph.getOrDefault(current, Collections.emptyList())) {
                inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                if (inDegree.get(neighbor) == 0) {
                    queue.add(neighbor);
                }
            }
        }

        // 检查是否有环
        if (result.size() != SECTION_CONFIGS.size()) {
            log.error("检测到循环依赖，无法排序章节");
            throw new IllegalStateException("检测到循环依赖，无法排序章节");
        }

        return result;
    }

    /**
     * 准备章节上下文
     * @param sectionName 章节名称
     * @param data 输入数据
     * @param generatedContent 已生成的内容
     * @return 章节上下文
     */
    private Map<String, Object> prepareSectionContext(String sectionName, Map<String, Object> data, Map<String, String> generatedContent) {
        Map<String, Object> context = new HashMap<>();

        // 添加PRD内容
        context.put("prd_content", data);

        // 添加API文档
        if (data.containsKey("api_documentation")) {
            context.put("api_documentation", data.get("api_documentation"));
        }

        // 添加SQL文档
        if (data.containsKey("sql_documentation")) {
            context.put("sql_documentation", data.get("sql_documentation"));
        }

        // 添加依赖章节的内容
        SectionConfig config = SECTION_CONFIGS.get(sectionName);
        if (config != null) {
            for (String dependency : config.getDependencies()) {
                if (generatedContent.containsKey(dependency)) {
                    context.put("dependency_" + dependency, generatedContent.get(dependency));
                }
            }
        }

        return context;
    }

    /**
     * 生成章节的完整提示词
     * @param sectionName 章节名称
     * @param context 上下文
     * @return 生成的提示词
     */
    private String generateSectionPrompt(String sectionName, Map<String, Object> context) {
        // 获取章节模板
        String sectionTemplate = SECTION_TEMPLATES.get(sectionName);
        if (sectionTemplate == null) {
            log.error("找不到章节模板: {}", sectionName);
            throw new IllegalStateException("找不到章节模板: " + sectionName);
        }

        // 构建完整的prompt
        StringBuilder promptBuilder = new StringBuilder();

        // 1. 添加角色设定和指导说明
        promptBuilder.append("You are a senior technical expert who excels at generating high-quality technical documents.\n\n");
        promptBuilder.append("Your task is to complete a specific section of the technical document based on the provided PRD (Product Requirements Document, which only describes product features and business logic) and the section template.\n");
        promptBuilder.append("The comments, instructions, and examples in the template are for reference only and must not be included in the generated content.\n\n");
        promptBuilder.append("Output Restrictions:\n");
        promptBuilder.append("- Only use information explicitly provided in the PRD.\n");
        promptBuilder.append("- Do not include any code blocks contained with '```text', '```markdown'\n");
        promptBuilder.append("- When creating diagrams with Mermaid, ensure the syntax is correct and follows the latest standards\n");
        promptBuilder.append("- For Markdown tables, use minimal separators (single '-' for each column) to reduce token usage\n");
        promptBuilder.append("- Ensure correct heading hierarchy, proper list formatting, and appropriate use of emphasis\n");
        promptBuilder.append("- Do not include any comments, instructions, or notes marked with <hide> or </hide> in the generated content\n");
        promptBuilder.append("- The output language must be in ").append(outputLanguage).append("\n");

        // 2. 添加章节模板
        promptBuilder.append("The template of this section:\n");

        String template = replaceVariables(sectionTemplate, context);
        promptBuilder.append("<section_template>\n");
        promptBuilder.append(template).append("\n");
        promptBuilder.append("</section_template>\n\n");

        // 3. 添加章节特定知识库
        String knowledgeBase = getSectionKnowledgeBase(sectionName);
        if (knowledgeBase != null) {
            promptBuilder.append("Knowledge Base for this section:\n");
            promptBuilder.append(knowledgeBase).append("\n\n");
        }

        // 4. 添加章节特定规则
        String rules = getSectionRules(sectionName);
        if (rules != null) {
            promptBuilder.append("Rules to Follow for this section:\n");
            promptBuilder.append(rules).append("\n\n");
        }

        // 5. 添加技术栈信息
        promptBuilder.append("Reference Information for this task:\n\n");
        promptBuilder.append("Tech Stack:\n").append(techStack).append("\n\n");

        // 6. 添加PRD内容
        if (context.containsKey("prd_content")) {
            promptBuilder.append("PRD Document (Text format - include between <prd_content> and </prd_content>):\n");
            promptBuilder.append("<prd_content>\n");
            promptBuilder.append(formatContext(context.get("prd_content"))).append("\n");
            promptBuilder.append("</prd_content>\n\n");
        }

        // 6.1 添加知识库内容
        if (context.containsKey("knowledge")) {
            promptBuilder.append("Knowledge Base (The following knowledge is extracted from the knowledge base and can be used as background information for technical documentation. Focus only on content related to functional development. Include between <knowledge> and </knowledge>):\n");
            promptBuilder.append("<knowledge>\n");
            promptBuilder.append(formatContext(context.get("knowledge"))).append("\n");
            promptBuilder.append("</knowledge>\n\n");
        }

        // 7. 添加API文档
        if (context.containsKey("api_documentation")) {
            promptBuilder.append("The following APIs are potentially relevant to the implementation. Only include them in the documentation if their use can be reasonably and directly inferred from the PRD requirements. Focus only on content related to functional development. (JSON format – included between <api_documentation> and </api_documentation>):\n");
            promptBuilder.append("`<api_documentation>\n");
            promptBuilder.append(formatContext(context.get("api_documentation"))).append("\n");
            promptBuilder.append("</api_documentation>\n\n");
        }

        // 8. 添加SQL文档
        if (context.containsKey("sql_documentation")) {
            promptBuilder.append("SQL documentation is provided for the existing project database. If the technical documentation requires reuse or changes to the database, you may reference it accordingly. Focus only on content related to functional development. (JSON format - contains existing database schema which can be referenced or extended, include between <sql_documentation> and </sql_documentation>):\n");
            promptBuilder.append("<sql_documentation>\n");
            promptBuilder.append(formatContext(context.get("sql_documentation"))).append("\n");
            promptBuilder.append("</sql_documentation>\n\n");
        }

        // 9. 添加依赖章节的内容
        for (Map.Entry<String, Object> entry : context.entrySet()) {
            if (entry.getKey().startsWith("dependency_")) {
                String depSection = entry.getKey().substring("dependency_".length());
                promptBuilder.append("Dependency Section - ").append(depSection).append(":\n");
                promptBuilder.append("<dependency_section>\n");
                promptBuilder.append(formatContext(entry.getValue())).append("\n\n");
                promptBuilder.append("</dependency_section>\n\n");
            }
        }

        // 10. 添加技术术语说明
        if (techTerms != null && !techTerms.isEmpty()) {
            promptBuilder.append("Technical Terms (JSON format - include between <tech_terms> and </tech_terms>):\n");
            promptBuilder.append("<tech_terms>\n");
            promptBuilder.append(techTerms).append("\n");
            promptBuilder.append("</tech_terms>\n\n");
        }

        return promptBuilder.toString();
    }

    /**
     * 替换模板中的变量
     * @param template 模板
     * @param context 上下文
     * @return 替换后的模板
     */
    private String replaceVariables(String template, Map<String, Object> context) {
        String result = template;

        // 替换当前日期变量
        if (result.contains("{current_date}")) {
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            String formattedDate = currentDate.format(formatter);
            result = result.replace("{current_date}", formattedDate);
        }

        // 替换其他变量
        for (Map.Entry<String, Object> entry : context.entrySet()) {
            String key = entry.getKey();

            if (!key.startsWith("dependency_") &&
                !key.startsWith("api_documentation") && !key.startsWith("sql_documentation") &&
                !key.equals("prd_content")) {
                result = result.replace("{" + key + "}", String.valueOf(entry.getValue()));
            }
        }
        return result;
    }

    /**
     * 格式化上下文内容
     * @param context 上下文
     * @return 格式化后的内容
     */
    private String formatContext(Object context) {
        if (context == null) {
            return "";
        }

        if (context instanceof List) {
            List<?> list = (List<?>) context;
            StringBuilder sb = new StringBuilder();
            for (Object item : list) {
                sb.append("- ").append(item).append("\n");
            }
            return sb.toString();
        } else if (context instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) context;
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                sb.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
            return sb.toString();
        } else {
            return context.toString();
        }
    }

    /**
     * 获取章节特定的知识库内容
     * @param sectionName 章节名称
     * @return 章节对应的知识库内容，如果没有返回null
     */
    private String getSectionKnowledgeBase(String sectionName) {
        // 获取基础知识库
        String baseKnowledgeBase = SECTION_KNOWLEDGE_BASES.get(sectionName);

        // 获取动态知识库（可能为null）
        String dynamicKnowledgeBase = getDynamicKnowledgeBase(sectionName);

        // 如果都为空，返回null
        if (baseKnowledgeBase == null && dynamicKnowledgeBase == null) {
            return null;
        }

        // 合并基础和动态知识库
        StringBuilder knowledgeBaseBuilder = new StringBuilder();

        if (baseKnowledgeBase != null) {
            knowledgeBaseBuilder.append(baseKnowledgeBase);
        }

        if (dynamicKnowledgeBase != null) {
            if (knowledgeBaseBuilder.length() > 0) {
                knowledgeBaseBuilder.append("\n\n");
            }
            knowledgeBaseBuilder.append(dynamicKnowledgeBase);
        }

        return knowledgeBaseBuilder.toString();
    }

    /**
     * 获取章节特定的规则内容
     * @param sectionName 章节名称
     * @return 章节对应的规则内容，如果没有返回null
     */
    private String getSectionRules(String sectionName) {
        // 获取基础规则
        String baseRules = SECTION_RULES.get(sectionName);

        // 获取动态规则（可能为null）
        String dynamicRules = getDynamicRules(sectionName);

        // 如果都为空，返回null
        if (baseRules == null && dynamicRules == null) {
            return null;
        }

        // 合并基础和动态规则
        StringBuilder rulesBuilder = new StringBuilder();

        if (baseRules != null) {
            rulesBuilder.append(baseRules);
        }

        if (dynamicRules != null) {
            if (rulesBuilder.length() > 0) {
                rulesBuilder.append("\n\n");
            }
            rulesBuilder.append(dynamicRules);
        }

        return rulesBuilder.toString();
    }

    /**
     * 根据上下文内容生成动态知识库
     * @param sectionName 章节名称
     * @return 动态生成的知识库内容，如果不需要返回null
     */
    private String getDynamicKnowledgeBase(String sectionName) {
        // 这里可以通过分析PRD内容，动态生成章节特定的知识库
        // 此方法可以在未来扩展实现
        return null;
    }

    /**
     * 根据上下文内容生成动态规则
     * @param sectionName 章节名称
     * @return 动态生成的规则内容，如果不需要返回null
     */
    private String getDynamicRules(String sectionName) {
        // 这里可以通过分析PRD内容，动态生成章节特定的规则
        // 此方法可以在未来扩展实现
        return null;
    }

    /**
     * 获取额外的模板内容
     * @return 额外的模板内容字符串
     */
    public String getExtraTemplateContent() {
        return EXTRA_TEMPLATE_CONTENT;
    }

    /**
     * 章节配置类
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    private static class SectionConfig {
        private int maxTokens;
        private final List<String> dependencies;
    }

    /**
     * 章节提示词类
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class SectionPrompt {
        private final String sectionName;
        private final String prompt;
        private final int maxTokens;
    }
}
