package com.xiaomi.mone.codeflow.service.feishu;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class FeiShuStorageRedisService {

    private StringRedisTemplate stringRedisTemplate;

    @NacosValue("${redis.url:}")
    private String redisUrl;

    @NacosValue("${redis.password:}")
    private String redisPassword;

    private static final String REDIS_KEY_PREFIX = "mone-codeflow:";
    private static final String ACCESS_TOKEN_SUFFIX = ":feishu_access_token";
    private static final String REFRESH_TOKEN_SUFFIX = ":feishu_refresh_token";

    @PostConstruct
    public void init() {
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
        String[] urlParts = redisUrl.split(":");
        redisConfig.setHostName(urlParts[0]);
        redisConfig.setPort(Integer.parseInt(urlParts[1]));
        redisConfig.setPassword(redisPassword);

        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(redisConfig);
        connectionFactory.afterPropertiesSet();

        stringRedisTemplate = new StringRedisTemplate(connectionFactory);
        stringRedisTemplate.afterPropertiesSet();
    }

    public String getAccessToken(String account) {
        String accessTokenKey = REDIS_KEY_PREFIX + account + ACCESS_TOKEN_SUFFIX;
        return stringRedisTemplate.opsForValue().get(accessTokenKey);
    }

    public String getRefreshToken(String account) {
        String refreshTokenKey = REDIS_KEY_PREFIX + account + REFRESH_TOKEN_SUFFIX;
        return stringRedisTemplate.opsForValue().get(refreshTokenKey);
    }

    public void saveAccessToken(String account, String accessToken, long expiresIn) {
        String accessTokenKey = REDIS_KEY_PREFIX + account + ACCESS_TOKEN_SUFFIX;
        stringRedisTemplate.opsForValue().set(accessTokenKey, accessToken, expiresIn - 60, TimeUnit.SECONDS);
    }

    public void saveRefreshToken(String account, String refreshToken, long refreshExpiresIn) {
        String refreshTokenKey = REDIS_KEY_PREFIX + account + REFRESH_TOKEN_SUFFIX;
        stringRedisTemplate.opsForValue().set(refreshTokenKey, refreshToken, refreshExpiresIn - 60, TimeUnit.SECONDS);
    }
} 