package com.xiaomi.mone.codeflow.service.llm;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.xiaomi.mone.codeflow.service.config.AppConfig;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP (Model Context Protocol) 服务
 * 通过langchain4j调用MCP功能
 */
@Slf4j
@Service
public class McpService {

    private final ChatLanguageModel chatModel;
    private final AppConfig appConfig;
    private final RestTemplate restTemplate;

    @NacosValue(value = "${mcp.enabled:true}", autoRefreshed = true)
    private boolean mcpEnabled;

    @NacosValue(value = "${mcp.server.url:http://localhost:3000}", autoRefreshed = true)
    private String mcpServerUrl;

    @NacosValue(value = "${mcp.timeout:30}", autoRefreshed = true)
    private int mcpTimeout;

    @NacosValue(value = "${mcp.max-tokens:4096}", autoRefreshed = true)
    private int mcpMaxTokens;

    public McpService(ChatLanguageModel chatModel, AppConfig appConfig, RestTemplate restTemplate) {
        this.chatModel = chatModel;
        this.appConfig = appConfig;
        this.restTemplate = restTemplate;
    }



    /**
     * 处理MCP问题查询
     *
     * @param question 用户问题
     * @return MCP处理结果
     */
    public String processQuestion(String question) {
        log.info("开始处理MCP问题: {}", question);

        if (!mcpEnabled) {
            log.warn("MCP功能已禁用");
            return "MCP功能当前已禁用，请联系管理员启用。";
        }

        try {
            // 1. 首先通过MCP服务器获取上下文信息
            String mcpContext = getMcpContext(question);

            // 2. 构建增强的提示词
            String enhancedPrompt = buildEnhancedPrompt(question, mcpContext);

            // 3. 使用langchain4j调用大模型
            return callLanguageModel(enhancedPrompt);

        } catch (Exception e) {
            log.error("处理MCP问题时发生错误", e);
            return "处理问题时发生错误: " + e.getMessage();
        }
    }

    /**
     * 从MCP服务器获取上下文信息
     *
     * @param question 用户问题
     * @return MCP上下文信息
     */
    private String getMcpContext(String question) {
        try {
            log.info("从MCP服务器获取上下文信息");

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("question", question);
            requestBody.put("timeout", mcpTimeout);
            requestBody.put("maxTokens", mcpMaxTokens);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 调用MCP服务器
            ResponseEntity<String> response = restTemplate.exchange(
                mcpServerUrl + "/api/context",
                HttpMethod.POST,
                entity,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                String context = response.getBody();
                log.info("成功获取MCP上下文信息，长度: {}", context != null ? context.length() : 0);
                return context != null ? context : "";
            } else {
                log.warn("MCP服务器返回非成功状态码: {}", response.getStatusCode());
                return "";
            }

        } catch (Exception e) {
            log.error("获取MCP上下文信息失败", e);
            return "";
        }
    }

    /**
     * 构建增强的提示词
     *
     * @param question 原始问题
     * @param mcpContext MCP上下文信息
     * @return 增强的提示词
     */
    private String buildEnhancedPrompt(String question, String mcpContext) {
        StringBuilder promptBuilder = new StringBuilder();

        promptBuilder.append("请基于以下上下文信息回答用户问题：\n\n");

        if (mcpContext != null && !mcpContext.trim().isEmpty()) {
            promptBuilder.append("上下文信息：\n");
            promptBuilder.append(mcpContext);
            promptBuilder.append("\n\n");
        }

        promptBuilder.append("用户问题：\n");
        promptBuilder.append(question);
        promptBuilder.append("\n\n");
        promptBuilder.append("请提供准确、详细的回答。如果上下文信息不足以回答问题，请说明需要更多信息。");

        return promptBuilder.toString();
    }

    /**
     * 使用langchain4j调用大模型
     *
     * @param prompt 提示词
     * @return 大模型响应
     */
    private String callLanguageModel(String prompt) {
        try {
            log.info("调用大模型处理增强提示词");

            // 创建系统消息
            SystemMessage systemMessage = SystemMessage.from(
                "你是一个智能助手，擅长基于提供的上下文信息回答用户问题。" +
                "请仔细分析上下文信息，提供准确、有用的回答。"
            );

            // 创建用户消息
            UserMessage userMessage = UserMessage.from(prompt);

            // 根据配置的最大令牌数创建模型实例
            ChatLanguageModel modelToUse;
            if (mcpMaxTokens > 0) {
                modelToUse = appConfig.createChatModelWithMaxTokens(mcpMaxTokens);
            } else {
                modelToUse = chatModel;
            }

            // 调用大模型
            Response<AiMessage> response = modelToUse.generate(systemMessage, userMessage);

            if (response != null && response.content() != null) {
                String result = response.content().text();
                log.info("大模型响应成功，响应长度: {}", result.length());
                return result;
            } else {
                throw new IllegalStateException("大模型返回结果为空");
            }

        } catch (Exception e) {
            log.error("调用大模型失败", e);
            throw new RuntimeException("调用大模型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查MCP服务状态
     *
     * @return MCP服务是否可用
     */
    public boolean checkMcpStatus() {
        if (!mcpEnabled) {
            return false;
        }

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                mcpServerUrl + "/api/health",
                HttpMethod.GET,
                null,
                String.class
            );

            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.error("检查MCP服务状态失败", e);
            return false;
        }
    }

    /**
     * 获取MCP配置信息
     *
     * @return MCP配置信息
     */
    public Map<String, Object> getMcpConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", mcpEnabled);
        config.put("serverUrl", mcpServerUrl);
        config.put("timeout", mcpTimeout);
        config.put("maxTokens", mcpMaxTokens);
        config.put("status", checkMcpStatus());
        return config;
    }
}
