package com.xiaomi.mone.codeflow.service.llm;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import com.xiaomi.mone.codeflow.service.config.AppConfig;
import com.xiaomi.mone.codeflow.service.exception.RDGeneratorException;
import com.xiaomi.mone.codeflow.service.template.RDTemplateManager;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;

import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.databind.ObjectMapper;

@Slf4j
@Service
public class RDGeneratorStreamService {
    
    private final ChatLanguageModel chatModel;
    private final AppConfig appConfig;
    private final RDTemplateManager templateManager;
    private final KnowledgeExtractorService knowledgeExtractorService;
    
    public RDGeneratorStreamService(
            ChatLanguageModel chatModel,
            AppConfig appConfig,
            RDTemplateManager templateManager,
            KnowledgeExtractorService knowledgeExtractorService) {
        this.chatModel = chatModel;
        this.appConfig = appConfig;
        this.templateManager = templateManager;
        this.knowledgeExtractorService = knowledgeExtractorService;
    }
    
    /**
     * 流式生成RD文档，增加知识库背景参数
     */
    public void generateStream(String prdContent, String apiDocContent, String sqlDocContent, List<String> knowledges, Consumer<String> chunkConsumer) {
        log.info("开始流式生成RD文档");
        try {
            // 准备输入数据
            Map<String, Object> inputData = new HashMap<>();
            inputData.put("prd_content", prdContent);
            if (apiDocContent != null && !apiDocContent.isEmpty()) {
                inputData.put("api_documentation", apiDocContent);
            }
            if (sqlDocContent != null && !sqlDocContent.isEmpty()) {
                inputData.put("sql_documentation", sqlDocContent);
            }
            if (knowledges != null && !knowledges.isEmpty()) {
                inputData.put("knowledge", knowledges);
            }
            // 获取章节提示词列表（已按照依赖关系排序）
            List<RDTemplateManager.SectionPrompt> sectionPrompts = templateManager.generateSectionPrompts(inputData);
            log.info("总共需要生成 {} 个章节", sectionPrompts.size());
            // 创建章节内容缓存
            Map<String, String> generatedContent = new HashMap<>();
            // 按顺序逐个生成章节内容
            for (int i = 0; i < sectionPrompts.size(); i++) {
                RDTemplateManager.SectionPrompt sectionPrompt = sectionPrompts.get(i);
                String sectionName = sectionPrompt.getSectionName();
                log.info("开始生成章节[{}/{}]: {}", (i+1), sectionPrompts.size(), sectionName);
                try {
                    // 发送进度事件
                    Map<String, Object> progressEvent = new HashMap<>();
                    progressEvent.put("current", i + 1);
                    progressEvent.put("total", sectionPrompts.size());
                    // 使用XML风格标签包装progress事件
                    chunkConsumer.accept(String.format("<event:progress>%s</event:progress>", new ObjectMapper().writeValueAsString(progressEvent)));
                    
                    // 生成章节内容
                    String sectionContent = generateSectionContent(sectionPrompt.getPrompt(), sectionPrompt.getMaxTokens());
                    // 缓存生成的内容
                    generatedContent.put(sectionName, sectionContent);
                    // 从章节名称中提取编号
                    int order = RDTemplateManager.finalOrderedSections.indexOf(sectionName) + 1;
                    // 发送章节内容，使用XML风格标签包裹
                    String chapterInfo = String.format("<chapter:%d>%s\n\n</chapter:%d>", order, sectionContent, order);
                    chunkConsumer.accept(chapterInfo);
                    log.info("章节[{}/{}] {} 生成完成，内容字节数: {}", 
                        (i+1), 
                        sectionPrompts.size(), 
                        sectionName,
                        sectionContent.getBytes().length);
                } catch (Exception e) {
                    log.error("生成章节 {} 时发生错误", sectionName, e);
                    // 继续生成下一个章节，不中断整个流程
                }
            }
            log.info("RD文档流式生成完成");
        } catch (Exception e) {
            log.error("生成RD文档时发生错误", e);
            throw new RDGeneratorException("生成RD文档失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 使用提示词生成特定章节内容
     * 
     * @param sectionPrompt 章节提示词
     * @param maxTokens 最大token数
     * @return 生成的章节内容
     * @throws RestClientException 如果API调用失败
     */
    private String generateSectionContent(String sectionPrompt, int maxTokens) {
        String content = generateDocument(sectionPrompt, maxTokens);
        
        // 过滤markdown代码块标记
        content = content.replaceAll("^```markdown\\s*", "");  // 移除开头的```markdown
        content = content.replaceAll("\\s*```$", "");         // 移除结尾的```
        content = content.trim();                            // 移除首尾空白
        
        return content;
    }
    
    /**
     * 使用提示词生成文档内容
     * 
     * @param prompt 提示词
     * @param maxTokens 最大token数
     * @return 生成的文档内容
     * @throws RestClientException 如果API调用失败
     */
    private String generateDocument(String prompt, int maxTokens) {
        log.info("调用API生成章节内容，最大tokens: {}", maxTokens);
        
        try {
            // 创建系统消息
            SystemMessage systemMessage = SystemMessage.from("You are a senior technical expert who excels at generating technical documentation based on PRD product documents.");
            // 创建用户消息
            UserMessage userMessage = UserMessage.from(prompt);
            
            // 根据maxTokens创建新的模型实例或使用默认实例
            ChatLanguageModel modelToUse;
            if (maxTokens > 0) {
                modelToUse = appConfig.createChatModelWithMaxTokens(maxTokens);
            } else {
                modelToUse = chatModel;
            }
            
            // 使用langchain4j发送请求
            Response<AiMessage> response = modelToUse.generate(systemMessage, userMessage);
            
            if (response != null && response.content() != null) {
                return response.content().text();
            } else {
                throw new IllegalStateException("API返回结果格式错误：响应为空或没有生成内容");
            }
            
        } catch (Exception e) {
            throw new RestClientException("生成文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用大模型提取术语及关键词
     */
    public List<KnowledgeExtractorService.KnowledgeQuery> extractKnowledgeQueriesByLLM(String prdContent) {
        String prompt = RDTemplateManager.TERM_EXTRACTION_PROMPT_TEMPLATE.replace("{prdContent}", prdContent);
        try {
            dev.langchain4j.data.message.SystemMessage systemMessage = dev.langchain4j.data.message.SystemMessage.from("You are a senior technical expert who excels at extracting technical terms from PRD documents.");
            dev.langchain4j.data.message.UserMessage userMessage = dev.langchain4j.data.message.UserMessage.from(prompt);
            dev.langchain4j.model.output.Response<dev.langchain4j.data.message.AiMessage> response = chatModel.generate(systemMessage, userMessage);
            String output = response != null && response.content() != null ? response.content().text() : "";
            return knowledgeExtractorService.extractKnowledgeQueries(output);
        } catch (Exception e) {
            log.error("调用大模型提取术语失败", e);
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 获取RDTemplateManager实例
     * @return RDTemplateManager实例
     */
    public RDTemplateManager getRDTemplateManager() {
        return templateManager;
    }
} 