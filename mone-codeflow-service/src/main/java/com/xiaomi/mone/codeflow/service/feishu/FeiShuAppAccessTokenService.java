package com.xiaomi.mone.codeflow.service.feishu;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonParseException;
import com.lark.oapi.Client;
import com.lark.oapi.service.auth.v3.model.*;
import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import com.xiaomi.mone.codeflow.service.feishu.results.FeiShuAppAccessTokenResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class FeiShuAppAccessTokenService extends FeiShuBaseService {

    public FeiShuAppAccessTokenResult getInternalAppAccessToken() throws Exception {
        log.info("FeiShuAppAccessTokenService getInternalAppAccessToken");
        // 构建client
        Client client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();

        // 创建请求对象
        InternalAppAccessTokenReq req = InternalAppAccessTokenReq.newBuilder()
                .internalAppAccessTokenReqBody(InternalAppAccessTokenReqBody.newBuilder()
                        .appId(this.getAppId())
                        .appSecret(this.getAppSecret())
                        .build())
                .build();

        // 发起请求
        InternalAppAccessTokenResp resp = client.auth().v3().appAccessToken().internal(req);

        // 获取原始响应体
        String rawResponseBody = new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8);
        log.info("FeiShuAppAccessTokenService getInternalAppAccessToken raw response: {}", rawResponseBody);

        FeiShuAppAccessTokenResult result = new FeiShuAppAccessTokenResult();

        try {
            JsonObject jsonObject = JsonParser.parseString(rawResponseBody).getAsJsonObject();

            if (jsonObject.has("app_access_token")) {
                result.setAppAccessToken(jsonObject.get("app_access_token").getAsString());
            }
            if (jsonObject.has("tenant_access_token")) {
                result.setTenantAccessToken(jsonObject.get("tenant_access_token").getAsString());
            }
            if (jsonObject.has("expire")) {
                result.setExpire(jsonObject.get("expire").getAsInt());
            }

            int apiCode = jsonObject.has("code") ? jsonObject.get("code").getAsInt() : -1;
            String apiMsg = jsonObject.has("message") ? jsonObject.get("message").getAsString() : "";

            if (apiCode != 0) {
                result.setCode(ResultCodeEnum.FAIL);
                result.setMsg(apiMsg);
            }else{
                result.setCode(ResultCodeEnum.SUCCESS);
            }
        } catch (JsonParseException e) {
            log.error("Failed to parse raw response body as JsonObject: {}", rawResponseBody, e);
            result.setCode(ResultCodeEnum.FAIL);
            result.setMsg("Failed to parse raw response body as JsonObject");
        } catch (Exception e) {
             log.error("An unexpected error occurred during initial parsing of raw response body: {}", rawResponseBody, e);
             result.setCode(ResultCodeEnum.FAIL);
             result.setMsg("An unexpected error occurred during initial parsing of raw response body");
        }

        return result;
    }
}