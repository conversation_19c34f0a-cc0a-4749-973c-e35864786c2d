package com.xiaomi.mone.codeflow.service.feishu.results;

import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FeiShuAppAccessTokenResult extends BaseResult {

    private String appAccessToken;
    private String tenantAccessToken;
    private Integer expire;

    public FeiShuAppAccessTokenResult(ResultCodeEnum code) {
        super(code);
    }

    public FeiShuAppAccessTokenResult() {
        super(ResultCodeEnum.SUCCESS);
    }
} 