package com.xiaomi.mone.codeflow.service.feishu;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.docx.v1.model.*;
import com.xiaomi.mone.codeflow.service.feishu.results.FeiShuCreateDocumentResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class FeiShuUserDocumentService extends FeiShuBaseService {

    private Client client;

    public FeiShuUserDocumentService() {
        this.client = Client.newBuilder(this.getAppId(), this.getAppSecret())
                .openBaseUrl(this.getOpenBaseUrl())
                .build();
    }

    public FeiShuCreateDocumentResult createDocument(String userAccessToken) throws Exception {
        log.info("FeiShuUserDocumentService createDocument");
        
        // 创建请求对象
        CreateDocumentReq req = CreateDocumentReq.newBuilder()
                .createDocumentReqBody(CreateDocumentReqBody.newBuilder()
                        .build())
                .build();

        // 发起请求
        CreateDocumentResp resp = client.docx().v1().document().create(req, RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build());

        // 获取原始响应体
        String rawResponseBody = new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8);
        log.info("FeiShuUserDocumentService createDocument raw response: {}", rawResponseBody);

        FeiShuCreateDocumentResult result = new FeiShuCreateDocumentResult();

        try {
            JsonObject jsonObject = JsonParser.parseString(rawResponseBody).getAsJsonObject();
            int apiCode = jsonObject.has("code") ? jsonObject.get("code").getAsInt() : -1;

            if (apiCode == 0 && jsonObject.has("data")) {
                JsonObject data = jsonObject.getAsJsonObject("data");
                if (data.has("document")) {
                    JsonObject document = data.getAsJsonObject("document");
                    if (document.has("document_id")) {
                        String documentId = document.get("document_id").getAsString();
                        result.setFeishuUrl(this.getOpenBaseUrl() + "/docx/" + documentId);
                        return result;
                    }
                }
            }

            log.error("Failed to get document_id from response: {}", rawResponseBody);
            result.setFeishuUrl("");
        } catch (Exception e) {
            log.error("An error occurred while processing response: {}", rawResponseBody, e);
            result.setFeishuUrl("");
        }

        return result;
    }

    public Boolean updateDocumentTitle(String userAccessToken, String title) throws Exception {
        log.info("FeiShuUserDocumentService updateDocumentTitle");
        
        // 创建请求对象
        PatchDocumentBlockReq req = PatchDocumentBlockReq.newBuilder()
                .updateBlockRequest(UpdateBlockRequest.newBuilder()
                        .updateTextElements(UpdateTextElementsRequest.newBuilder()
                                .elements(new TextElement[] {
                                        TextElement.newBuilder()
                                                .textRun(TextRun.newBuilder()
                                                        .content(title)
                                                        .build())
                                                .build()
                                })
                                .build())
                        .build())
                .build();

        // 发起请求
        PatchDocumentBlockResp resp = client.docx().v1().documentBlock().patch(req, RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build());

        // 获取原始响应体
        String rawResponseBody = new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8);
        log.info("FeiShuUserDocumentService updateDocumentTitle raw response: {}", rawResponseBody);

        try {
            JsonObject jsonObject = JsonParser.parseString(rawResponseBody).getAsJsonObject();
            int apiCode = jsonObject.has("code") ? jsonObject.get("code").getAsInt() : -1;
            return apiCode == 0;
        } catch (Exception e) {
            log.error("An error occurred while processing response: {}", rawResponseBody, e);
            return false;
        }
    }
} 