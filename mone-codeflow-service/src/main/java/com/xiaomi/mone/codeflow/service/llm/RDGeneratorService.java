package com.xiaomi.mone.codeflow.service.llm;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.*;
import java.util.Set;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.io.File;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import com.xiaomi.mone.codeflow.service.config.AppConfig;
import com.xiaomi.mone.codeflow.service.template.RDTemplateManager;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Data
public class RDGeneratorService {
    
    private final ChatLanguageModel chatModel;
    private final AppConfig appConfig;
    private final RDTemplateManager templateManager;
    
    @NacosValue(value = "${llm.api-model:gemini-2.0-flash}", autoRefreshed = true)
    private String apiModel;
    
    @NacosValue(value = "${llm.output-language:Simple Chinese}", autoRefreshed = true)
    private String outputLanguage;

    @NacosValue(value = "${llm.max-tokens:32768}", autoRefreshed = true)
    private int maxTokens;
    
    @NacosValue(value = "${pandoc.installation.path.windows:}", autoRefreshed = true)
    private String pandocPathWindows;
    
    @NacosValue(value = "${pandoc.installation.path.linux:}", autoRefreshed = true)
    private String pandocPathLinux;
    
    @NacosValue(value = "${pandoc.installation.path.mac:}", autoRefreshed = true)
    private String pandocPathMac;
    
    @NacosValue(value = "${mermaid.filter.path.windows:}", autoRefreshed = true)
    private String mermaidFilterWindows;
    
    @NacosValue(value = "${mermaid.filter.path.linux:}", autoRefreshed = true)
    private String mermaidFilterLinux;
    
    @NacosValue(value = "${mermaid.filter.path.mac:}", autoRefreshed = true)
    private String mermaidFilterMac;
    
    @NacosValue(value = "${node.path.windows:}", autoRefreshed = true)
    private String nodePathWindows;
    
    @NacosValue(value = "${node.path.linux:}", autoRefreshed = true)
    private String nodePathLinux;
    
    @NacosValue(value = "${node.path.mac:}", autoRefreshed = true)
    private String nodePathMac;
    
    public RDGeneratorService(ChatLanguageModel chatModel, AppConfig appConfig, RDTemplateManager templateManager) {
        this.chatModel = chatModel;
        this.appConfig = appConfig;
        this.templateManager = templateManager;
    }
    
    public String generate(String prdContent, String apiDocContent, String sqlDocContent) throws IOException {
        log.info("开始生成RD文档");
        
        // 准备输入数据
        Map<String, Object> inputData = new HashMap<>();
        inputData.put("prd_content", prdContent);
        
        if (apiDocContent != null) {
            inputData.put("api_documentation", apiDocContent);
        }
        if (sqlDocContent != null) {
            inputData.put("sql_documentation", sqlDocContent);
        }
        
        // 获取章节提示词列表
        List<RDTemplateManager.SectionPrompt> sectionPrompts = templateManager.generateSectionPrompts(inputData);
        
        // 逐个生成章节内容
        Map<String, String> generatedContent = new HashMap<>();
        
        for (RDTemplateManager.SectionPrompt sectionPrompt : sectionPrompts) {
            log.info("生成章节: {}", sectionPrompt.getSectionName());
            
            // 调用API生成章节内容
            String sectionContent = generateSectionContent(
                sectionPrompt.getPrompt(), 
                sectionPrompt.getMaxTokens()
            );
            
            // 保存章节内容用于依赖传递和最终拼接
            generatedContent.put(sectionPrompt.getSectionName(), sectionContent);
        }
        
        // 按照固定顺序拼接生成的章节内容
        StringBuilder rdContentBuilder = new StringBuilder();
        // 章节按照固定顺序拼接
        for (String sectionName : RDTemplateManager.finalOrderedSections) {
            if (generatedContent.containsKey(sectionName)) {
                rdContentBuilder.append(generatedContent.get(sectionName)).append("\n\n");
            }
        }
        
        String rdContent = rdContentBuilder.toString();
        
        return rdContent;
    }
    
    /**
     * 使用提示词生成特定章节内容
     * 
     * @param sectionPrompt 章节提示词
     * @param maxTokens 最大token数
     * @return 生成的章节内容
     * @throws RestClientException 如果API调用失败
     */
    private String generateSectionContent(String sectionPrompt, int maxTokens) {
        String content = generateDocument(sectionPrompt, maxTokens);
        
        // 过滤markdown代码块标记
        content = content.replaceAll("^```markdown\\s*", "");  // 移除开头的```markdown
        content = content.replaceAll("\\s*```$", "");         // 移除结尾的```
        content = content.trim();                            // 移除首尾空白
        
        return content;
    }
    
    /**
     * 使用提示词生成文档内容
     * 
     * @param prompt 提示词
     * @param maxTokens 最大token数
     * @return 生成的文档内容
     * @throws RestClientException 如果API调用失败
     */
    private String generateDocument(String prompt, int maxTokens) {
        log.info("调用API生成文档，使用模型: {}, 最大tokens: {}", apiModel, maxTokens);
        
        try {
            // 创建系统消息
            SystemMessage systemMessage = SystemMessage.from("You are a senior technical expert who excels at generating technical documentation based on PRD product documents.");
            // 创建用户消息
            UserMessage userMessage = UserMessage.from(prompt);
            
            // 根据maxTokens创建新的模型实例或使用默认实例
            ChatLanguageModel modelToUse;
            if (maxTokens > 0) {
                modelToUse = appConfig.createChatModelWithMaxTokens(maxTokens);
            } else {
                modelToUse = chatModel;
            }
            
            // 使用langchain4j发送请求
            Response<AiMessage> response = modelToUse.generate(systemMessage, userMessage);
            
            if (response != null && response.content() != null) {
                return response.content().text();
            } else {
                throw new IllegalStateException("API返回结果格式错误：响应为空或没有生成内容");
            }
            
        } catch (Exception e) {
            throw new RestClientException("生成文档失败: " + e.getMessage(), e);
        }
    }
    
    public void saveToFile(String content, String outputPath) throws IOException {
        log.info("保存RD文档到: {}", outputPath);
        Files.write(Paths.get(outputPath), content.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * 获取当前操作系统的pandoc路径
     * 
     * @return pandoc可执行文件路径
     */
    private String getPandocPath() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            return pandocPathWindows;
        } else if (os.contains("nix") || os.contains("nux")) {
            return pandocPathLinux;
        } else if (os.contains("mac")) {
            return pandocPathMac;
        } else {
            throw new IllegalStateException("不支持的操作系统: " + os);
        }
    }
    
    /**
     * 获取mermaid-filter路径
     * 
     * @return mermaid-filter的完整路径
     */
    private String getMermaidFilterParam() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            return mermaidFilterWindows;
        } else if (os.contains("nix") || os.contains("nux")) {
            return mermaidFilterLinux;
        } else if (os.contains("mac")) {
            return mermaidFilterMac;
        } else {
            throw new IllegalStateException("不支持的操作系统: " + os);
        }
    }
    
    /**
     * 获取Node.js路径
     * 
     * @return Node.js可执行文件路径
     */
    private String getNodePath() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            return nodePathWindows;
        } else if (os.contains("nix") || os.contains("nux")) {
            return nodePathLinux;
        } else if (os.contains("mac")) {
            return nodePathMac;
        } else {
            throw new IllegalStateException("不支持的操作系统: " + os);
        }
    }
    
    /**
     * 生成Markdown文档并转换为docx格式
     * 
     * @param prdContent PRD内容
     * @param apiDocContent API文档内容
     * @param sqlDocContent SQL文档内容
     * @return 生成的docx文件路径
     * @throws IOException 如果文件操作失败
     */
    public String generateToDocx(String prdContent, String apiDocContent, String sqlDocContent) throws IOException {
        // 生成Markdown内容
        String mdContent = generate(prdContent, apiDocContent, sqlDocContent);
        
        // 调用Markdown转Docx方法
        return convertMarkdownToDocx(mdContent);
    }

    public String uploadMarkdown(String mdContent) throws IOException {
        log.info("开始上传Markdown文件");
        
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("rd-doc-");
        
        // 生成唯一文件名
        String mdFileName = "rd-doc-" + UUID.randomUUID().toString() + ".md";
        
        // 使用原始文件名保存文件
        Path filePath = tempDir.resolve(mdFileName);
        
        // 保存文件
        Files.write(filePath, mdContent.getBytes(StandardCharsets.UTF_8));
        log.info("Markdown文件已保存到: {}", filePath);
        
        return filePath.toString();
    }
    
    /**
     * 将Markdown内容转换为Docx文件
     * 
     * @param markdownContent Markdown格式的内容
     * @return 生成的Docx文件路径
     * @throws IOException 如果文件操作失败
     */
    public String convertMarkdownToDocx(String markdownContent) throws IOException {
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("rd-doc-");
        
        // 生成唯一文件名
        String mdFileName = "rd-doc-" + UUID.randomUUID().toString() + ".md";
        String docxFileName = mdFileName.replace(".md", ".docx");
        
        // 创建文件路径
        Path mdFilePath = tempDir.resolve(mdFileName);
        Path docxFilePath = tempDir.resolve(docxFileName);
        
        // 保存Markdown文件
        Files.write(mdFilePath, markdownContent.getBytes(StandardCharsets.UTF_8));
        log.info("保存Markdown文件到: {}", mdFilePath);
        
        try {
            // 获取pandoc路径
            String pandocPath = getPandocPath();
            
            // 获取mermaid-filter路径
            String mermaidFilter = getMermaidFilterParam();
            
            log.info("使用pandoc路径: {}", pandocPath);
            log.info("使用mermaid-filter: {}", mermaidFilter);
            
            // 使用ProcessBuilder的列表形式构建命令
            ProcessBuilder processBuilder = new ProcessBuilder(
                pandocPath,
                "-F", mermaidFilter,
                mdFilePath.toString(),
                "-t", "docx",
                "-o", docxFilePath.toString()
            );
            
            // 设置环境变量
            Map<String, String> env = processBuilder.environment();
            String nodePath = getNodePath();
            if (nodePath != null && !nodePath.isEmpty()) {
                log.info("添加Node.js路径到环境变量: {}", nodePath);
                String pathEnv = env.get("PATH");
                if (pathEnv != null) {
                    // 在PATH环境变量前添加node路径
                    env.put("PATH", nodePath + File.pathSeparator + pathEnv);
                } else {
                    env.put("PATH", nodePath);
                }
            }
            
            // 设置重定向错误流到标准输出流
            processBuilder.redirectErrorStream(true);

            // 设置工作目录为系统临时目录，避免权限问题
            processBuilder.directory(new File(System.getProperty("java.io.tmpdir")));
            
            log.info("执行命令: {}", String.join(" ", processBuilder.command()));
            
            // 启动进程
            Process process = processBuilder.start();
            
            // 捕获命令输出用于调试
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info("命令输出: {}", line);
                }
            }
            
            int exitCode = process.waitFor();
            
            if (exitCode != 0) {
                throw new IOException("Pandoc转换失败，退出码: " + exitCode);
            }
            
            log.info("成功转换docx文件: {}", docxFilePath);
            return docxFilePath.toString();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Pandoc转换被中断", e);
        }
    }
} 