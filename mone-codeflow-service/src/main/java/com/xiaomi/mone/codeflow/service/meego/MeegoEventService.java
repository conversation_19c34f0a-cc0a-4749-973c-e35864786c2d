package com.xiaomi.mone.codeflow.service.meego;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaomi.mone.codeflow.common.enums.FileTypeEnum;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuUploadFileService;
import com.xiaomi.mone.codeflow.service.feishu.IProjectWorkItemService;
import com.xiaomi.mone.codeflow.service.llm.RDGeneratorService;
import com.xiaomi.mone.codeflow.common.enums.WebhookEventType;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuDocumentService;
import com.xiaomi.mone.codeflow.service.feishu.entitys.DocumentEntity;
import com.xiaomi.mone.codeflow.service.feishu.results.DocumentResult;
import com.xiaomi.mone.codeflow.service.meego.entity.WebhookHeader;
import com.xiaomi.mone.codeflow.service.meego.entity.WorkitemUpdateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.alibaba.nacos.api.config.annotation.NacosValue;

import java.io.File;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.text.SimpleDateFormat;
import java.net.URLEncoder;

@Slf4j
@Service
public class MeegoEventService {

    private static final String PM_PRD_ALIAS = "pm_prd";

    @Autowired
    private FeiShuDocumentService feiShuDocumentService;

    @Autowired
    private FeiShuUploadFileService feiShuUploadFileService;

    private final RDGeneratorService rdGeneratorService;

    @Autowired
    private IProjectWorkItemService projectWorkItemService;

    @NacosValue(value = "${feishu.project.url.template:https://project.f.mioffice.cn/{space_name}/story/detail/{story_id}}")
    private String projectUrlPattern;

    @NacosValue(value = "${aidoc.placeholder.template}", autoRefreshed = true)
    private String aidocContentPattern;

    public MeegoEventService(RDGeneratorService rdGeneratorService) {
        this.rdGeneratorService = rdGeneratorService;
    }

    public void webhookHandler(String requestBody) {
        try {
            log.info("收到Meego webhook请求: {}", requestBody);

            // 解析请求体
            JSONObject jsonObject = JSON.parseObject(requestBody);

            // 获取header信息
            WebhookHeader header = JSON.parseObject(jsonObject.getString("header"), WebhookHeader.class);
            if (header == null || header.getEventType() == null) {
                log.error("无效的webhook请求: 缺少header或eventType");
                return;
            }

            // 获取payload
            Object payload = jsonObject.get("payload");
            if (payload == null) {
                log.error("无效的webhook请求: 缺少payload");
                return;
            }

            // 根据事件类型处理不同的业务逻辑
            WebhookEventType eventType = WebhookEventType.fromType(header.getEventType());
            if (eventType == null) {
                log.warn("未处理的事件类型: {}", header.getEventType());
                return;
            }

            switch (eventType) {
                case WORKITEM_CREATE:
                    handleWorkitemCreate(payload);
                    break;
                case WORKITEM_UPDATE:
                    handleWorkitemUpdate(payload);
                    break;
                case WORKITEM_FINISH:
                    handleWorkitemFinish(payload);
                    break;
                case WORKITEM_DELETE:
                    handleWorkitemDelete(payload);
                    break;
            }
        } catch (Exception e) {
            log.error("处理webhook请求失败", e);
        }

    }

    /**
     * 处理工作项创建事件
     */
    private void handleWorkitemCreate(Object payload) {
        // TODO: 实现工作项创建事件的处理逻辑
        log.info("处理工作项创建事件: {}", payload);
    }

    /**
     * 处理工作项更新事件
     */
    private void handleWorkitemUpdate(Object payloadJson) {
        try {
            log.info("开始处理工作项更新事件, payload: {}", payloadJson);

            // 1. 解析payload为WorkitemUpdateEvent.Payload对象
            WorkitemUpdateEvent.Payload payloadObj = JSON.parseObject(JSON.toJSONString(payloadJson),
                    WorkitemUpdateEvent.Payload.class);
            if (payloadObj == null) {
                log.error("解析WorkitemUpdateEvent.Payload失败");
                return;
            }

            // 2. 获取变更的字段
            List<WorkitemUpdateEvent.ChangedField> changedFields = payloadObj.getChangedFields();
            if (CollectionUtils.isEmpty(changedFields)) {
                log.info("无字段变更信息");
                return;
            }

            // 3. 检查PRD文档字段变更
            Optional<WorkitemUpdateEvent.ChangedField> prdFieldOpt = changedFields.stream()
                    .filter(field -> PM_PRD_ALIAS.equals(field.getFieldAlias()))
                    .findFirst();

            if (!prdFieldOpt.isPresent()) {
                log.info("未包含PRD文档字段更新");
                return;
            }

            WorkitemUpdateEvent.ChangedField prdField = prdFieldOpt.get();
            Object curValue = prdField.getCurFieldValue();
            if (curValue == null) {
                log.warn("PRD文档字段当前值为空");
                return;
            }
            log.info("PRD文档连接：{}", curValue);

            String docUrl = String.valueOf(curValue);
            if (StringUtils.isEmpty(docUrl)) {
                log.warn("PRD文档URL为空");
                return;
            }

            // 4. 获取文档内容
            String documentId = parseDocumentId(docUrl);
            if (StringUtils.isEmpty(documentId)) {
                log.error("无法解析文档ID, docUrl: {}", docUrl);
                return;
            }

            // 异步处理文档生成和创建
            processDocumentAsync(payloadObj, documentId);
        } catch (Exception e) {
            log.error("处理工作项更新事件异常, payload: {}", payloadJson, e);
        }
    }

    /**
     * 异步处理文档生成和创建
     */
    private void processDocumentAsync(WorkitemUpdateEvent.Payload payloadObj, String documentId) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始异步处理文档生成, workItemId: {}, documentId: {}", payloadObj.getId(), documentId);

                // 1. 生成临时技术文档，引导去AIDoc.
                String projectSimpleName = payloadObj.getProjectSimpleName() == null ? "" : payloadObj.getProjectSimpleName();
                String projectUrl = projectUrlPattern.replace("{space_name}", projectSimpleName).replace("{story_id}", String.valueOf(payloadObj.getId()));
                String docContent = aidocContentPattern.replace("{project_url_encoded}", java.net.URLEncoder.encode(projectUrl, "UTF-8"));

                // 2. 构建文档实体
                DocumentEntity documentEntity = new DocumentEntity();
                documentEntity.setTitle(String.format("技术方案设计 - %s", System.currentTimeMillis()));
                documentEntity.setFileType(FileTypeEnum.DOCX);
                documentEntity.setContent(docContent);
                // 可选：设置folderToken

                // 3. 创建飞书文档
                DocumentResult documentResult = feiShuDocumentService.createDocument(documentEntity);

                // 4. 回填文档链接
                projectWorkItemService.updateRdDocField(payloadObj.getId(), documentResult.getUrl());
                log.info("文档处理完成, workItemId: {}, documentId: {}, result: {}", payloadObj.getId(), documentId, documentResult);
            } catch (Exception e) {
                log.error("异步处理文档异常, workItemId: {}, documentId: {}", payloadObj.getId(), documentId, e);
            }
        });
    }

    /**
     * 构建文档实体
     */
    private DocumentEntity buildDocumentEntity(String documentId) {
        DocumentEntity documentEntity = new DocumentEntity();
        documentEntity.setDocumentId(documentId);
        documentEntity.setTitle(String.format("技术方案设计 - %s", System.currentTimeMillis()));
        documentEntity.setFileType(FileTypeEnum.DOCX);
        return documentEntity;
    }

    /**
     * 解析文档URL获取文档ID
     *
     * @param docUrl 飞书文档URL
     * @return 文档ID，如果URL为空或格式不正确则返回null
     */
    public String parseDocumentId(String docUrl) {
        if (StringUtils.isEmpty(docUrl)) {
            return null;
        }

        int lastSlashIndex = docUrl.lastIndexOf("/");
        if (lastSlashIndex == -1 || lastSlashIndex == docUrl.length() - 1) {
            return null;
        }

        return docUrl.substring(lastSlashIndex + 1);
    }

    /**
     * 处理工作项完成事件
     */
    private void handleWorkitemFinish(Object payload) {
        // TODO: 实现工作项完成事件的处理逻辑
        log.info("处理工作项完成事件: {}", payload);
    }

    /**
     * 处理工作项删除事件
     */
    private void handleWorkitemDelete(Object payload) {
        // TODO: 实现工作项删除事件的处理逻辑
        log.info("处理工作项删除事件: {}", payload);
    }
}
