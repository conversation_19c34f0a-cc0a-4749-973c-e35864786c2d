package com.xiaomi.mone.codeflow.service.llm;

import static org.junit.jupiter.api.Assertions.*;

import java.time.Duration;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.output.Response;
import lombok.extern.slf4j.Slf4j;

/**
 * 用于测试langchain4j库集成
 */
@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
class LangchainTest {

    @Value("${llm.api-key}")
    private String apiKey;

    @Value("${llm.base-url}")
    private String baseUrl;

    @Value("${llm.api-model}")
    private String modelName;

    @Test
    void testLangchain4j() {
        log.info("测试langchain4j库集成");
        // 创建一个简单的模型实例用于测试
        ChatLanguageModel model = OpenAiChatModel.builder()
                .baseUrl(baseUrl)
                .apiKey(apiKey)
                .modelName(modelName)
                .temperature(0.7)
                .maxTokens(100)
                .timeout(Duration.ofSeconds(30))
                .build();

        // 创建测试消息
        SystemMessage systemMessage = SystemMessage.from("你是一个测试助手");
        UserMessage userMessage = UserMessage.from("测试一下");

        // 发送测试请求
        Response<AiMessage> response = model.generate(systemMessage, userMessage);

        // 检查响应
        if (response != null && response.content() != null) {
            log.info("langchain4j测试响应: {}", response.content().text());
        } else {
            throw new IllegalStateException("langchain4j测试失败：响应为空");
        }

        log.info("langchain4j库集成测试成功");
    }
}