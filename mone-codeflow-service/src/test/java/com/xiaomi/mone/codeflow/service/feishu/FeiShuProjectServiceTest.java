package com.xiaomi.mone.codeflow.service.feishu;

import com.lark.project.Client;
import com.lark.project.service.workitem.builder.UpdateWorkItemResp;
import com.xiaomi.mone.codeflow.service.FeiShuProjectConfig;
import com.xiaomi.mone.codeflow.service.feishu.exception.FeishuProjectException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.context.TestPropertySource;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
@TestPropertySource(locations = "classpath:application-test.properties")
public class FeiShuProjectServiceTest {

    @InjectMocks
    private IProjectWorkItemService projectWorkItemService;

    @Mock
    private Client mockClient;

    @Mock
    private FeiShuProjectConfig config;

    // 测试数据常量
    private static final long VALID_WORK_ITEM_ID = 2520791L;
    private static final long INVALID_WORK_ITEM_ID = 0L;
    private static final String VALID_DOC_URL = "https://xiaomi.f.mioffice.cn/docx/doxk4mCBzrmnB7oyRh2h0P6Qgig";
    private static final String EMPTY_DOC_URL = "";
    private static final String NULL_DOC_URL = null;
    private static final String LONG_DOC_URL = "https://xiaomi.f.mioffice.cn/docx/" + generateLongString(1000);

    // 配置常量
    private static final String PROJECT_KEY = "test-project-key";
    private static final String API_USER_KEY = "test-api-user-key";
    private static final String RD_DOC_FIELD_KEY = "test-rd-doc-field-key";
    private static final String RD_DOC_ALIAS = "test-rd-doc-alias";

    private static String generateLongString(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append("a");
        }
        return sb.toString();
    }

    @Before
    public void setUp() throws Exception {
        // 设置配置mock
        when(config.getProjectKey()).thenReturn(PROJECT_KEY);
        when(config.getApiUserKey()).thenReturn(API_USER_KEY);
        when(config.getWorkItemRdDocFieldKey()).thenReturn(RD_DOC_FIELD_KEY);
        when(config.getWorkItemRdDocAlias()).thenReturn(RD_DOC_ALIAS);

        // 设置mock client的行为
        UpdateWorkItemResp successResp = new UpdateWorkItemResp();
        successResp.setErrCode(0);
        successResp.setErrMsg("success");

        when(mockClient.getWorkItemService())
                .thenReturn(Mockito.mock(com.lark.project.service.workitem.WorkItemService.class));
        when(mockClient.getWorkItemService().updateWorkItem(any(), any())).thenReturn(successResp);
    }

    /**
     * 测试正常更新场景
     */
    @Test
    public void testUpdateRdDocField_Success() throws Exception {
        // 执行测试
        projectWorkItemService.updateRdDocField(VALID_WORK_ITEM_ID, VALID_DOC_URL);

        // 验证调用
        Mockito.verify(mockClient.getWorkItemService()).updateWorkItem(any(), any());
    }

    /**
     * 测试无效的工作项ID
     */
    @Test(expected = FeishuProjectException.class)
    public void testUpdateRdDocField_InvalidWorkItemId() throws Exception {
        projectWorkItemService.updateRdDocField(INVALID_WORK_ITEM_ID, VALID_DOC_URL);
    }

    /**
     * 测试空的文档URL
     */
    @Test(expected = FeishuProjectException.class)
    public void testUpdateRdDocField_EmptyUrl() throws Exception {
        projectWorkItemService.updateRdDocField(VALID_WORK_ITEM_ID, EMPTY_DOC_URL);
    }

    /**
     * 测试null的文档URL
     */
    @Test(expected = FeishuProjectException.class)
    public void testUpdateRdDocField_NullUrl() throws Exception {
        projectWorkItemService.updateRdDocField(VALID_WORK_ITEM_ID, NULL_DOC_URL);
    }

    /**
     * 测试API返回错误
     */
    @Test(expected = FeishuProjectException.class)
    public void testUpdateRdDocField_ApiError() throws Exception {
        // 设置API错误响应
        UpdateWorkItemResp errorResp = new UpdateWorkItemResp();
        errorResp.setErrCode(40001);
        errorResp.setErrMsg("Invalid request");

        when(mockClient.getWorkItemService().updateWorkItem(any(), any())).thenReturn(errorResp);

        projectWorkItemService.updateRdDocField(VALID_WORK_ITEM_ID, VALID_DOC_URL);
    }

    /**
     * 测试网络异常
     */
    @Test(expected = FeishuProjectException.class)
    public void testUpdateRdDocField_NetworkError() throws Exception {
        // 模拟网络异常
        when(mockClient.getWorkItemService().updateWorkItem(any(), any()))
                .thenThrow(new RuntimeException("Network error"));

        projectWorkItemService.updateRdDocField(VALID_WORK_ITEM_ID, VALID_DOC_URL);
    }

    /**
     * 测试超长URL
     */
    @Test(expected = FeishuProjectException.class)
    public void testUpdateRdDocField_LongUrl() throws Exception {
        projectWorkItemService.updateRdDocField(VALID_WORK_ITEM_ID, LONG_DOC_URL);
    }

    /**
     * 测试特殊字符URL
     */
    @Test(expected = FeishuProjectException.class)
    public void testUpdateRdDocField_SpecialCharsUrl() throws Exception {
        String specialCharsUrl = "https://xiaomi.f.mioffice.cn/docx/!@#$%^&*()";
        projectWorkItemService.updateRdDocField(VALID_WORK_ITEM_ID, specialCharsUrl);
    }
}
