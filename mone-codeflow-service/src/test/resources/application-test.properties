#server
app.name=mone-codeflow
server.type=dev
server.port=8089
server.debug=true

dubbo.group=dev
dubbo.protocol.port=-1
dubbo.registry.address=nacos://nacos.test.b2c.srv:80
nacos.config.addrs=nacos.test.b2c.srv:80

log.path=/tmp

# 大模型openai配置
llm.base-url=https://gateway.ai.cloudflare.com/v1/1de711151e0011488df69f3629b2945a/aiproxy/google-ai-studio/v1beta/openai/
llm.api-key=AIzaSyCezHaWwQpr_D0oIn_pF-htzWJ7IpninPg
llm.api-model=gemini-2.0-flash
llm.max-tokens=32768
llm.output-language=Simple Chinese
llm.timeout=60

# pandoc配置
pandoc.installation.path.windows=C:\\Program Files\\Pandoc\\pandoc.exe
pandoc.installation.path.linux=/usr/local/bin/pandoc
pandoc.installation.path.mac=/usr/local/bin/pandoc

# mermaid-filter 配置
mermaid.filter.path.windows=mermaid-filter.cmd
mermaid.filter.path.linux=mermaid-filter
mermaid.filter.path.mac=/usr/local/lib/node_modules/bin/mermaid-filter

# Node.js 路径配置
node.path.windows=C:\\Program Files\\nodejs
node.path.linux=/usr/bin
node.path.mac=/usr/local/bin