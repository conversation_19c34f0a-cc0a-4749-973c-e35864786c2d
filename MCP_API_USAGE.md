# MCP (Model Context Protocol) API 使用说明

## 概述

本项目集成了MCP功能，使用langchain4j原生MCP支持来增强AI问答能力。通过MCP协议，AI模型可以访问外部工具和服务，大大扩展了其能力范围。

## 功能特性

- 使用langchain4j原生MCP客户端
- 支持SSE (Server-Sent Events) 传输协议
- 自动工具发现和集成
- AI助手与MCP工具的无缝集成
- 智能回退机制（MCP不可用时使用普通模型）
- 提供健康检查和状态监控
- 统一的API响应格式

## API 接口

### 1. MCP问题查询

**接口地址：** `POST /api/mcp/query`

**请求参数：**
- `question` (String, 必填): 用户问题

**请求示例：**
```bash
curl -X POST "http://localhost:8080/api/mcp/query" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "question=如何使用Spring Boot开发REST API？"
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": "基于上下文信息，Spring Boot开发REST API的步骤如下：..."
}
```

### 2. MCP服务状态查询

**接口地址：** `GET /api/mcp/status`

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "enabled": true,
    "serverUrl": "http://localhost:3000",
    "timeout": 30,
    "maxTokens": 4096,
    "status": true
  }
}
```

### 3. MCP健康检查

**接口地址：** `GET /api/mcp/health`

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": "healthy"
}
```

## 配置说明

在各环境的配置文件中添加以下MCP相关配置：

```properties
# MCP配置
mcp.enabled=true                           # 是否启用MCP功能
mcp.server.url=http://localhost:3000/mcp/sse  # MCP服务器SSE端点地址
mcp.timeout=30                             # 请求超时时间（秒）
mcp.max-tokens=4096                        # 最大令牌数
```

### 配置文件位置

- 开发环境: `mone-codeflow-server/src/main/resources/config/dev.properties`
- 预览环境: `mone-codeflow-server/src/main/resources/config/preview.properties`
- 测试环境: `mone-codeflow-server/src/main/resources/config/staging.properties`
- 生产环境: `mone-codeflow-server/src/main/resources/config/c3.properties`, `c4.properties`

## 工作流程

1. **服务初始化**: 启动时自动初始化MCP客户端和AI助手
2. **接收用户问题**: 通过`/api/mcp/query`接口接收用户问题
3. **工具发现**: MCP客户端自动发现并注册可用的工具
4. **智能处理**: AI助手根据问题内容自动选择和调用相关MCP工具
5. **结果整合**: 将工具执行结果与AI推理结合，生成最终回答
6. **回退机制**: 如果MCP不可用，自动回退到普通模型处理

## 错误处理

- 当MCP功能被禁用时，返回相应提示信息
- 当MCP服务器不可用时，仍会尝试使用原始问题调用大模型
- 所有异常都会被捕获并返回友好的错误信息

## 注意事项

1. **MCP服务器要求**: 需要部署支持SSE传输的MCP服务器，提供`/mcp/sse`端点
2. **网络连接**: 确保应用服务器能够访问MCP服务器的SSE端点
3. **性能考虑**: MCP工具调用会增加响应时间，建议合理设置超时时间
4. **配置管理**: 通过Nacos配置中心管理MCP相关配置，支持动态刷新
5. **依赖版本**: 需要langchain4j 1.0.1或更高版本以支持MCP功能
6. **回退机制**: 当MCP不可用时，系统会自动回退到普通模型，确保服务可用性

## 开发说明

### 核心类说明

- **McpService**: MCP服务核心逻辑，使用langchain4j原生MCP客户端
- **McpController**: MCP API控制器，提供REST接口
- **AppConfig**: 应用配置类，提供langchain4j相关配置

### 依赖关系

- 依赖现有的`ChatLanguageModel` Bean
- 使用langchain4j的`McpClient`和`McpToolProvider`
- 使用`@NacosValue`注解进行配置注入
- 自动初始化和清理MCP资源

### 新增功能

- **自动初始化**: 使用`@PostConstruct`自动初始化MCP客户端
- **资源管理**: 使用`@PreDestroy`自动清理MCP连接
- **智能回退**: MCP不可用时自动使用普通模型
- **状态监控**: 提供详细的MCP客户端状态信息

## 扩展建议

1. **多MCP服务器支持**: 可以配置多个MCP客户端，连接不同的MCP服务器
2. **工具过滤**: 使用`McpToolProvider`的过滤功能，只启用特定的MCP工具
3. **异步处理**: 对于耗时的MCP工具调用，可以考虑异步处理机制
4. **监控和告警**: 添加MCP客户端连接状态的监控和告警机制
5. **权限控制**: 根据用户权限控制对不同MCP工具的访问
6. **工具配置**: 为不同的MCP工具配置不同的超时时间和参数
