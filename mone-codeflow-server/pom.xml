<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mone-codeflow</artifactId>
        <groupId>com.xiaomi.mone</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mone-codeflow-server</artifactId>

    <dependencies>
        <!-- 内部模块依赖 -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>mone-codeflow-service</artifactId>
        </dependency>

        <!-- 小米内部依赖 -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-server-registry</artifactId>
            <version>2.7.12-mone-SNAPSHOT</version>
        </dependency>


        <!-- Spring相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <!-- Dubbo相关 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- 测试相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources/META-INF</directory>
                <filtering>true</filtering>
                <includes>
                    <include>app.properties</include>
                </includes>
                <targetPath>META-INF/</targetPath>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.xiaomi.mone.codeflow.server.MoneCodeflowBootstrap</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>src/main/resources/config/dev.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>staging</id>
            <properties>
                <profiles.active>staging</profiles.active>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/staging.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>c3</id>
            <properties>
                <profiles.active>c3</profiles.active>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/c3.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>c4</id>
            <properties>
                <profiles.active>c4</profiles.active>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/c4.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>preview</id>
            <properties>
                <profiles.active>preview</profiles.active>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/preview.properties</filter>
                </filters>
            </build>
        </profile>
    </profiles>

</project>