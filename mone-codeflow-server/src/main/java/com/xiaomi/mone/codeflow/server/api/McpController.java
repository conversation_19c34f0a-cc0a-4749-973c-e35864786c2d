package com.xiaomi.mone.codeflow.server.api;

import com.xiaomi.mone.codeflow.dto.common.ApiResponse;
import com.xiaomi.mone.codeflow.service.llm.McpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * MCP (Model Context Protocol) 控制器
 * 提供MCP相关的API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/mcp")
public class McpController {

    private final McpService mcpService;

    public McpController(McpService mcpService) {
        this.mcpService = mcpService;
    }

    /**
     * 处理MCP问题查询
     *
     * @param question 用户问题
     * @return 处理结果
     */
    @PostMapping("/query")
    public ResponseEntity<ApiResponse<String>> queryMcp(@RequestParam String question) {
        log.info("收到MCP查询请求，问题: {}", question);

        try {
            // 参数验证
            if (question == null || question.trim().isEmpty()) {
                log.warn("问题参数为空");
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("问题参数不能为空"));
            }

            // 调用MCP服务处理问题
            String result = mcpService.processQuestion(question.trim());

            log.info("MCP查询处理完成");
            return ResponseEntity.ok(ApiResponse.success(result));

        } catch (Exception e) {
            log.error("处理MCP查询时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("处理查询时发生错误: " + e.getMessage()));
        }
    }

    /**
     * GET方法不支持提示
     */
    @GetMapping("/query")
    public ResponseEntity<ApiResponse<String>> queryMcpMethodNotAllowed() {
        log.info("收到GET请求访问/query接口");
        return ResponseEntity.badRequest()
            .body(ApiResponse.error("该接口仅支持POST请求方式，请使用POST方法访问"));
    }

    /**
     * 获取MCP服务状态
     *
     * @return MCP服务状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMcpStatus() {
        log.info("收到MCP状态查询请求");

        try {
            Map<String, Object> status = mcpService.getMcpConfig();
            return ResponseEntity.ok(ApiResponse.success(status));

        } catch (Exception e) {
            log.error("获取MCP状态时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取MCP状态失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查接口
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> healthCheck() {
        log.debug("收到MCP健康检查请求");

        try {
            boolean isHealthy = mcpService.checkMcpStatus();
            String status = isHealthy ? "healthy" : "unhealthy";

            if (isHealthy) {
                return ResponseEntity.ok(ApiResponse.success(status));
            } else {
                return ResponseEntity.ok(ApiResponse.error("MCP服务不可用"));
            }

        } catch (Exception e) {
            log.error("MCP健康检查时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("健康检查失败: " + e.getMessage()));
        }
    }
}
