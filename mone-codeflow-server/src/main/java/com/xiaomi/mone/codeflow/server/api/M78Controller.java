package com.xiaomi.mone.codeflow.server.api;

import com.xiaomi.mone.codeflow.server.model.KnowledgeQueryRequest;
import com.xiaomi.mone.codeflow.common.ApiResponse;
import com.xiaomi.mone.codeflow.server.provider.KnowledgeService;
import com.xiaomi.mone.codeflow.service.llm.RDGeneratorStreamService;
import com.xiaomi.mone.codeflow.service.llm.KnowledgeExtractorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api")
public class M78Controller {

    @Autowired
    private KnowledgeService knowledgeService;

    @Autowired
    private RDGeneratorStreamService rdGeneratorStreamService;

    @PostMapping("/knowledge/query")
    public ResponseEntity<ApiResponse<List<String>>> queryKnowledge(@RequestBody KnowledgeQueryRequest request) {
        List<String> result = knowledgeService.queryKnowledge(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @PostMapping("/knowledge/extract")
    public ResponseEntity<ApiResponse<List<KnowledgeExtractorService.KnowledgeQuery>>> extractKnowledge(@RequestBody KnowledgeExtractRequest request) {
        List<KnowledgeExtractorService.KnowledgeQuery> data = rdGeneratorStreamService.extractKnowledgeQueriesByLLM(request.getPrdContent());
        return ResponseEntity.ok(ApiResponse.success(data));
    }

    public static class KnowledgeExtractRequest {
        private String prdContent;
        public String getPrdContent() { return prdContent; }
        public void setPrdContent(String prdContent) { this.prdContent = prdContent; }
    }
} 