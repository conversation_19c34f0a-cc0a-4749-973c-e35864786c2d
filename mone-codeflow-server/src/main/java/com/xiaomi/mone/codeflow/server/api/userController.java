package com.xiaomi.mone.codeflow.server.api;

import com.xiaomi.mone.codeflow.common.ApiResponse;
import com.xiaomi.mone.codeflow.dto.user.UserInfoResponse;
import com.xiaomi.mone.codeflow.dto.user.UserInfoVo;
import com.xiaomi.mone.codeflow.service.cas.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class userController {

    @Autowired
    private UserService userService;

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getUserInfo")
    public ApiResponse<UserInfoResponse> getUserInfo() {
        log.info("获取当前登录用户信息接口被调用");
        UserInfoVo userInfoVo = userService.getCurrentUserInfo();

        if (userInfoVo == null) {
            log.warn("未获取到当前登录用户信息");
            return ApiResponse.error(401, "用户未登录或获取用户信息失败");
        }

        UserInfoResponse userInfoResponse = new UserInfoResponse();

        userInfoResponse.setUserName(userInfoVo.getAccount());
        userInfoResponse.setName(userInfoVo.getName());
        userInfoResponse.setEmail(userInfoVo.getEmail());
        userInfoResponse.setDepartmentName(userInfoVo.getDepartmentName());

        log.info("成功获取用户信息: {}", userInfoResponse);
        return ApiResponse.success(userInfoResponse);
    }
}
