package com.xiaomi.mone.codeflow.server.provider;

import com.xiaomi.mone.codeflow.server.model.KnowledgeQueryRequest;
import com.xiaomi.mone.codeflow.server.model.KnowledgeQueryResponse;
import com.xiaomi.mone.codeflow.server.model.KnowledgeItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.beans.factory.annotation.Value;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.apache.dubbo.config.annotation.DubboReference;
import run.mone.ai.z.dto.ZKnowledgeReq;
import run.mone.ai.z.dto.ZKnowledgeRes;
import run.mone.ai.z.service.KnowledgeBaseService;
import com.xiaomi.youpin.infra.rpc.Result;

@Service
public class KnowledgeService {
    @Value("${m78.knowledge.api.url}")
    private String apiUrl;

    @Value("${m78.knowledge.api.api-key}")
    private String apiKey;

    @Autowired
    private RestTemplate restTemplate;

    @DubboReference(interfaceClass = KnowledgeBaseService.class, check = false, group = "${ref.ai.z.service.group}", version = "${ref.ai.z.service.version}", timeout = 10000)
    private KnowledgeBaseService zknowledgeBaseService;

    public List<String> queryKnowledge(KnowledgeQueryRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization", "Bearer " + apiKey);
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (request.getUserName() != null) params.add("userName", request.getUserName());
        if (request.getKnowledgeId() != null) params.add("knowledgeId", String.valueOf(request.getKnowledgeId()));
        if (request.getQueryText() != null) params.add("queryText", request.getQueryText());
        if (request.getLimit() != null) params.add("limit", String.valueOf(request.getLimit()));
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(params, headers);
        ResponseEntity<KnowledgeQueryResponse> responseEntity;
        try {
            responseEntity = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                entity,
                KnowledgeQueryResponse.class
            );
        } catch (Exception e) {
            return Collections.emptyList();
        }
        KnowledgeQueryResponse body = responseEntity.getBody();
        if (body != null && body.getCode() == 0 && body.getData() != null) {
            return body.getData().stream()
                .map(KnowledgeItem::getContent)
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public List<String> queryKnowledgeByNacos(KnowledgeQueryRequest request) {
        ZKnowledgeReq req = new ZKnowledgeReq();
        req.setKnowledgeBaseId(request.getKnowledgeId() == null ? null : request.getKnowledgeId().longValue());
        req.setUserName(request.getUserName());
        req.setQueryText(request.getQueryText());
        req.setLimit(request.getLimit() != null ? request.getLimit() : 1);
        Result<List<ZKnowledgeRes>> result = zknowledgeBaseService.querySimilarKnowledge(req);
        if (result != null && result.getCode() == 0 && result.getData() != null) {
            return result.getData().stream()
                .map(ZKnowledgeRes::getContent)
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
} 