package com.xiaomi.mone.codeflow.server.api;

import com.xiaomi.mone.codeflow.dto.common.ApiResponse;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuAppAccessTokenService;
import com.xiaomi.mone.codeflow.service.feishu.results.FeiShuAppAccessTokenResult;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuUserAccessTokenService;
import com.xiaomi.mone.codeflow.service.feishu.results.FeiShuUserAccessTokenResult;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuUserInfoService;
import com.xiaomi.mone.codeflow.service.feishu.results.FeiShuUserInfoDTO;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuStorageRedisService;
import com.xiaomi.mone.codeflow.service.cas.UserService;
import com.xiaomi.mone.codeflow.dto.user.UserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.xiaomi.mone.codeflow.common.enums.ResultCodeEnum;
import com.xiaomi.mone.codeflow.dto.feishu.FeishuAppAccessTokenResponse;
import com.xiaomi.mone.codeflow.dto.feishu.FeishuUserAccessTokenResponse;
import com.xiaomi.mone.codeflow.api.dto.FeiShuAuthRequestDTO;
import com.xiaomi.mone.codeflow.api.dto.FeiShuAuthResponseDTO;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuAuthParamsService;

@Slf4j
@RestController
@RequestMapping("/api")
public class FeishuController {

    @Autowired
    private FeiShuAppAccessTokenService feishuAppAccessTokenService;

    @Autowired
    private FeiShuUserAccessTokenService feiShuUserAccessTokenService;

    @Autowired
    private FeiShuUserInfoService feiShuUserInfoService;

    @Autowired
    private FeiShuAuthParamsService feiShuAuthParamsService;

    @Autowired
    private FeiShuStorageRedisService feiShuStorageRedisService;

    @Autowired
    private UserService userService;

    @GetMapping("/feishu/getAppAccessToken")
    public ApiResponse<FeishuAppAccessTokenResponse> getFeishuAppAccessToken() {
        try {
            FeiShuAppAccessTokenResult result = feishuAppAccessTokenService.getInternalAppAccessToken();
            if (result != null) {
                if (result.getCode() != ResultCodeEnum.SUCCESS) {
                    return ApiResponse.error(result.getMsg());
                }
                FeishuAppAccessTokenResponse response = new FeishuAppAccessTokenResponse();
                response.setAppAccessToken(result.getAppAccessToken());
                response.setTenantAccessToken(result.getTenantAccessToken());
                response.setExpire(result.getExpire());
                return ApiResponse.success(response);
            } else {
                return ApiResponse.error("Failed to get Feishu access token: service returned null");
            }
        } catch (Exception e) {
            log.error("Error getting Feishu app access token", e);
            return ApiResponse.error("Error getting Feishu app access token: " + e.getMessage());
        }
    }

    @PostMapping("/feishu/getUserAccessToken")
    public ApiResponse<FeishuUserAccessTokenResponse> getUserAccessToken(@RequestParam String code) {
        try {
            FeiShuUserAccessTokenResult result = feiShuUserAccessTokenService.getUserAccessToken(code);
            if (result != null) {
                if (result.getCode() != ResultCodeEnum.SUCCESS) {
                    return ApiResponse.error(result.getMsg());
                }
                FeishuUserAccessTokenResponse response = new FeishuUserAccessTokenResponse();
                response.setAccessToken(result.getAccessToken());
                response.setRefreshToken(result.getRefreshToken());
                response.setTokenType(result.getTokenType());
                response.setExpiresIn(result.getExpiresIn());
                response.setRefreshExpiresIn(result.getRefreshExpiresIn());
                response.setScope(result.getScope());
                return ApiResponse.success(response);
            } else {
                return ApiResponse.error("Failed to get Feishu user access token: service returned null");
            }
        } catch (Exception e) {
            log.error("Error getting Feishu app access token", e);
            return ApiResponse.error("Error getting Feishu app access token：" + e.getMessage());
        }
    }

    @PostMapping("/feishu/getUserInfo")
    public ApiResponse<FeiShuUserInfoDTO> getUserInfo() {
        try {
            UserInfoVo userInfoVo = userService.getCurrentUserInfo();
            if (userInfoVo == null) {
                return ApiResponse.error("User not logged in or failed to get user info");
            }

            log.info("Getting Feishu user info for account: {}", userInfoVo.getAccount());
            
            String accessToken = feiShuAuthParamsService.getAccessTokenByRedis(userInfoVo.getAccount());
            ApiResponse<FeiShuUserInfoDTO> result = feiShuUserInfoService.getUserInfo(accessToken);
            if (result != null) {
                return result;
            } else {
                return ApiResponse.error("Failed to get Feishu user info: service returned null");
            }
        } catch (Exception e) {
            log.error("Error getting Feishu user info", e);
            return ApiResponse.error("Error getting Feishu user info: " + e.getMessage());
        }
    }

    @PostMapping("/feishu/getAuthParams")
    public ApiResponse<FeiShuAuthResponseDTO> getAuthParams(@RequestBody FeiShuAuthRequestDTO request) {
        try {
            // 获取当前用户信息
            UserInfoVo userInfoVo = userService.getCurrentUserInfo();
            if (userInfoVo == null) {
                log.warn("Failed to get current user info");
                throw new RuntimeException("User not logged in or failed to get user info");
            }
            log.info("Getting Feishu auth params for account: {}", userInfoVo.getAccount());
            FeiShuAuthResponseDTO result = feiShuAuthParamsService.getAuthParams(userInfoVo.getAccount(), request.getFeiShuCode(), request.getPageUrl());
            if (result != null) {
                return ApiResponse.success(result);
            } else {
                return ApiResponse.error("Failed to get Feishu auth params: service returned null");
            }
        } catch (Exception e) {
            log.error("Error getting Feishu auth params", e);
            return ApiResponse.error("Error getting Feishu auth params: " + e.getMessage());
        }
    }
} 