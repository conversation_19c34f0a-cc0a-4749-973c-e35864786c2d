package com.xiaomi.mone.codeflow.server.api;

import com.xiaomi.mone.codeflow.service.feishu.IProjectWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.http.HttpStatus;
import com.xiaomi.mone.codeflow.common.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;

/**
 * Meego相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class MeegoController {

    @Autowired
    private IProjectWorkItemService projectWorkItemService;

    @NacosValue("${feishu.project-key}")
    private String projectKey;

    @NacosValue("${feishu.project.workitem.pmprd.field-ALIAS}")
    private String workItemPrdFieldAlias;

    @NacosValue("${feishu.project.workitem.rddoc.field-ALIAS}")
    private String workItemRddocFieldAlias;

    /**
     * 更新Meego工作项RD文档字段
     * @param workItemID 工作项ID
     * @param docUrl RD文档URL
     * @return 操作结果
     */
    @PostMapping("/updateMeegoRdDocField")
    public ResponseEntity<?> updateMeegoRdDocField(Long workItemID, String docUrl) {
        if (workItemID == null || workItemID <= 0) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), "参数错误：workItemID必须大于0"));
        }
        if (docUrl == null || docUrl.trim().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), "参数错误：docUrl不能为空"));
        }
        try {
            log.info("收到更新Meego工作项RD文档字段请求, workItemID: {}, rdDocUrl: {}", workItemID, docUrl);
            projectWorkItemService.updateRdDocField(workItemID, docUrl);
            return ResponseEntity.ok(ApiResponse.success("更新成功"));
        } catch (Exception e) {
            log.error("更新Meego工作项RD文档字段失败, workItemID: {}, docUrl: {}", workItemID, docUrl, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "更新失败: " + e.getMessage()));
        }
    }

    /**
     * 根据workItemID获取所有工作项
     */
    @GetMapping("/getAllWorkItemTypes")
    public ResponseEntity<?> getAllWorkItemTypes(@RequestParam Long workItemID) {
        if (workItemID == null || workItemID <= 0) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), "参数错误：workItemID必须大于0"));
        }
        try {
            java.util.List<?> allFields = projectWorkItemService.listAllWorkItemTypes(projectKey);
            return ResponseEntity.ok(ApiResponse.success(allFields));
        } catch (Exception e) {
            log.error("获取Meego字段信息失败, workItemID: {}", workItemID, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取字段失败: " + e.getMessage()));
        }
    }

    /**
     * 根据meegoUrl获取PRD文档url
     */
    @GetMapping("/getPrdUrlByMeegoUrl")
    public ResponseEntity<?> getPrdUrlByMeegoUrl(@RequestParam String meegoUrl) {
        Long workItemID = projectWorkItemService.extractWorkItemIdFromUrl(meegoUrl);
        if (workItemID == null || workItemID <= 0) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), "参数错误：meegoUrl无法解析出workItemID"));
        }
        try {
            java.util.List<?> detailList = projectWorkItemService.getRequirementDetail(projectKey, "story", workItemID);
            log.info("detailList: {}", new ObjectMapper().writeValueAsString(detailList));
            if (detailList != null && !detailList.isEmpty()) {
                Object detail = detailList.get(0);
                java.util.List<?> fields = null;
                if (detail instanceof java.util.Map) {
                    fields = (java.util.List<?>) ((java.util.Map<?, ?>) detail).get("fields");
                } else {
                    try {
                        java.lang.reflect.Field fieldsField = detail.getClass().getDeclaredField("fields");
                        fieldsField.setAccessible(true);
                        fields = (java.util.List<?>) fieldsField.get(detail);
                    } catch (Exception e) {
                        log.error("反射获取fields失败", e);
                    }
                }

                Object prdUrl = projectWorkItemService.getFieldValueByAlias(fields, workItemPrdFieldAlias);
                // Object rddocUrl = projectWorkItemService.getFieldValueByAlias(fields, workItemRddocFieldAlias);
                Map<String, Object> result = new HashMap<>();
                result.put("prd_url", prdUrl);
                // result.put("rddoc_url", rddocUrl);
                return ResponseEntity.ok(ApiResponse.success(result));
            } else {
                log.warn("未获取到有效的工作项详情, workItemID: {}", workItemID);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "未获取到有效的工作项详情"));
            }
        } catch (Exception e) {
            log.error("获取PRD字段失败, workItemID: {}", workItemID, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取PRD字段失败: " + e.getMessage()));
        }
    }
} 