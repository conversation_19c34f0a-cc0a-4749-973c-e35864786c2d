/*
 *  Copyright 2020 Xiaomi
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

package com.xiaomi.mone.codeflow.server.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.apache.commons.lang3.StringUtils;
import com.xiaomi.mone.tpc.login.filter.HttpReqUserFilter;
import com.xiaomi.mone.tpc.login.util.ConstUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfiguration {

    @NacosValue(value = "${aegis.sdk.public.key}", autoRefreshed = true)
    private String aegisSdk;

    @NacosValue(value = "${server.cas.ignoreUrl:''}", autoRefreshed = true)
    private String ignoreUrl;

    @NacosValue(value = "${token.parse.url}", autoRefreshed = true)
    private String tokenParseUrl;

    @NacosValue(value = "${cas.innerAuth}", autoRefreshed = true)
    private String innerAuth;

    @NacosValue(value = "${cas.devMode:'false'}", autoRefreshed = true)
    private String devMode;

   @Bean
    public FilterRegistrationBean filterCasBean() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setFilter(new HttpReqUserFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.addInitParameter(ConstUtil.innerAuth, innerAuth);
        registrationBean.addInitParameter(ConstUtil.authTokenUrl, tokenParseUrl);
        registrationBean.addInitParameter(ConstUtil.CAS_PUBLIC_KEY, aegisSdk);
        if (StringUtils.isNotBlank(ignoreUrl)) {
           registrationBean.addInitParameter(ConstUtil.ignoreUrl, ignoreUrl);
        }
        registrationBean.addInitParameter(ConstUtil.devMode, devMode);
        registrationBean.setOrder(1);
        return registrationBean;
    }
}
