// 生成技术文档
function generateDocument() {
    const prdContent = document.getElementById('prdContent').value;
    const feishuUrl = document.getElementById('feishuUrl').value;
    const meegoUrl = document.getElementById('meegoUrl').value;
    
    // 验证至少提供了一种输入
    if (!prdContent && !feishuUrl) {
        alert('请输入PRD内容或飞书文档URL');
        return;
    }

    const outputDiv = document.getElementById('output');
    const statusDiv = document.getElementById('status');
    const generateBtn = document.getElementById('generateBtn');
    
    // 更新UI状态
    outputDiv.innerHTML = '';
    statusDiv.textContent = '正在生成文档...';
    statusDiv.className = 'status-generating';
    generateBtn.disabled = true;

    // 用于存储章节内容的Map
    const chapterMap = new Map();
    let currentChapter = null;
    let currentContent = '';

    // 构建请求体
    const requestBody = {};
    if (feishuUrl) {
        requestBody.feishuUrl = feishuUrl;
    }
    if (prdContent) {
        requestBody.prdContent = prdContent;
    }
    if (meegoUrl) {
        requestBody.meegoUrl = meegoUrl;
    }

    // 使用fetch API建立SSE连接
    fetch('/aidoc/api/rd-generator/stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
        },
        body: JSON.stringify(requestBody)
    }).then(response => {
        if (!response.ok) {
            throw new Error('网络响应错误');
        }
        return response.body.getReader();
    }).then(reader => {
        const decoder = new TextDecoder();
        let buffer = '';

        function processChunk() {
            reader.read().then(({done, value}) => {
                if (done) {
                    // 流结束时，保存并显示最后一个章节的内容
                    if (currentChapter) {
                        chapterMap.set(currentChapter, currentContent);
                        displayChapter(currentChapter, currentContent);
                    }
                    statusDiv.textContent = '文档生成完成';
                    statusDiv.className = 'status-complete';
                    generateBtn.disabled = false;
                    return;
                }

                buffer += decoder.decode(value, {stream: true});
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data:')) {
                        const data = line.slice(5).trim();
                        if (data) {
                            // 检查是否是章节开始
                            const startMatch = data.match(/<chapter:(\d+)>(.*)/);
                            if (startMatch) {
                                // 保存并显示之前的章节内容
                                if (currentChapter) {
                                    chapterMap.set(currentChapter, currentContent);
                                    displayChapter(currentChapter, currentContent);
                                }
                                // 开始新的章节
                                currentChapter = parseInt(startMatch[1]);
                                // 提取标题作为新章节的第一行
                                const title = startMatch[2].trim();
                                currentContent = title;
                            }
                            // 检查是否是章节结束
                            else if (data.match(/<\/chapter:\d+>/)) {
                                if (currentChapter) {
                                    chapterMap.set(currentChapter, currentContent);
                                    displayChapter(currentChapter, currentContent);
                                    currentChapter = null;
                                    currentContent = '';
                                }
                            }
                            // 收集章节内容
                            else if (currentChapter) {
                                // 添加换行符和内容
                                currentContent += '\n' + data;
                            }
                        }
                    }
                }

                processChunk();
            }).catch(error => {
                console.error('读取流数据失败:', error);
                statusDiv.textContent = '读取数据失败，请刷新页面重试';
                statusDiv.className = 'status-error';
                generateBtn.disabled = false;
            });
        }

        processChunk();
    }).catch(error => {
        console.error('请求失败:', error);
        statusDiv.textContent = '连接错误，请刷新页面重试';
        statusDiv.className = 'status-error';
        generateBtn.disabled = false;
    });
}

// 显示单个章节
function displayChapter(order, content) {
    console.log('=== 显示章节调试信息 ===');
    console.log('章节编号:', order);
    console.log('原始内容:', content);
    console.log('内容长度:', content.length);
    console.log('内容是否为空:', !content);
    console.log('内容是否只包含空白字符:', !content.trim());
    
    // 将换行符转换为HTML换行
    const formattedContent = content.replace(/\n/g, '<br>');
    console.log('格式化后的内容:', formattedContent);
    
    // 创建章节容器
    const chapterDiv = document.createElement('div');
    chapterDiv.id = `chapter-${order}`;
    chapterDiv.innerHTML = formattedContent;
    
    const outputDiv = document.getElementById('output');
    
    // 找到合适的位置插入
    const chapters = Array.from(document.querySelectorAll('[id^="chapter-"]'));
    const insertBefore = chapters.find(chapter => {
        const chapterOrder = parseInt(chapter.id.split('-')[1]);
        return chapterOrder > order;
    });
    
    if (insertBefore) {
        console.log('插入位置: 在章节', insertBefore.id, '之前');
        outputDiv.insertBefore(chapterDiv, insertBefore);
    } else {
        console.log('插入位置: 追加到末尾');
        outputDiv.appendChild(chapterDiv);
    }
    
    outputDiv.scrollTop = outputDiv.scrollHeight;
    console.log('=== 章节显示完成 ===\n');
}

// 切换输入模式
function toggleInputMode() {
    const feishuInputContainer = document.getElementById('feishuInputContainer');
    const prdInputContainer = document.getElementById('prdInputContainer');
    const inputModeSelect = document.getElementById('inputModeSelect');
    
    // 隐藏所有输入容器
    feishuInputContainer.style.display = 'none';
    prdInputContainer.style.display = 'none';
    
    // 显示选中的输入容器
    switch(inputModeSelect.value) {
        case 'feishu':
            feishuInputContainer.style.display = 'block';
            break;
        default:
            prdInputContainer.style.display = 'block';
    }
} 