<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术文档生成测试</title>
    <link rel="stylesheet" href="css/rd-generator.css">
</head>
<body>
    <h1>技术文档生成测试</h1>
    <div class="container">
        <div class="input-section">
            <h2>输入信息</h2>
            
            <div class="input-container">
                <label for="inputModeSelect">选择输入方式：</label>
                <select id="inputModeSelect" onchange="toggleInputMode()">
                    <option value="prd">PRD文档内容</option>
                    <option value="feishu">飞书文档链接</option>
                </select>
            </div>
            
            <div id="feishuInputContainer" style="display: none;" class="input-container">
                <label for="feishuUrl">飞书文档链接：</label>
                <input type="text" id="feishuUrl" placeholder="请输入飞书文档链接，例如：https://xiaomi.f.mioffice.cn/docx/doxk4wwgPW3EWTkoohBXNUGKWRbggg">
            </div>
            
            <div id="prdInputContainer" class="input-container">
                <label for="prdContent">PRD文档内容：</label>
                <textarea id="prdContent" placeholder="请输入PRD文档内容..."></textarea>
            </div>
            
            <div class="input-container">
                <label for="meegoUrl">Meego需求链接：</label>
                <input type="text" id="meegoUrl" placeholder="请输入Meego需求链接">
            </div>
            
            <button id="generateBtn" onclick="generateDocument()">生成技术文档</button>
            <div id="status" class="status-waiting">准备就绪</div>
        </div>
        <div class="output-section">
            <h2>生成结果</h2>
            <div id="output" class="markdown-content"></div>
        </div>
    </div>

    <script src="js/rd-generator.js"></script>
</body>
</html> 