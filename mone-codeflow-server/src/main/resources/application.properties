#server
init.app.name=@app.name@
init.group=staging

app.name=@app.name@
server.type=@server.type@
server.port=@server.port@
server.debug=@server.debug@

dubbo.group=@dubbo.group@
dubbo.protocol.port=@dubbo.protocol.port@
dubbo.registry.address=@dubbo.registry.address@
nacos.config.addrs=@nacos.config.addrs@

log.path=@log.path@

ref.ai.z.service.group=@ref.ai.z.service.group@
ref.ai.z.service.version=@ref.ai.z.service.version@

spring.autoconfigure.exclude=com.mi.xms.sdk.NeptuneAutoConfiguration

# 允许Bean定义覆盖
spring.main.allow-bean-definition-overriding=true