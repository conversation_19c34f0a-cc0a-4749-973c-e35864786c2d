# MarkDown 转文档工具安装

## 安装 pandoc
https://github.com/jgm/pandoc/blob/main/INSTALL.md

## 安装 mermaid-filter 插件
```bash
npm install --global mermaid-filter
```

## 使用示例
```bash
pandoc -F mermaid-filter test.md -t docx -o test.docx 
```

## 生成文档
测试Demo：/rd-generator-test.html

生成接口
/api/rd-generator/stream
请求参数：
{
     "feishuUrl": "https://xiaomi.f.mioffice.cn/docx/doxk4wwgPW3EWTkoohBXNUGKWRbggg",
     "prdContent": "这里是PRD文档内容...",
     "apiDocContent": "API列表字符串",
     "sqlDocContent": "SQL列表字符串"
}
apiDocContent = 
[
    {
        "ProjectGroupsName": "项目组名",
        "ProjectName": "项目名",
        "ApiInfo": {
            "apiName": "接口名称",
            "apiPath": "接口路径",
            "requestParams": [
                "请求参数1",
                "请求参数2"
            ],
            "response": [
                "响应参数1",
                "响应参数2"
            ]
        }
    }
]

sqlDocContent = 
[
    {
        "Node": "节点路径",
        "table": ["tb1", "tb2"],
        "DbName": "数据库名",
        "sqlInfo": "建表语句"
    }
]

## 数据库Zeus Api
1、获取数据库列表
/api/zeus/get_database_list
入参：
{
    "email": "yanyugang",
    "treeId": 12088
}
返回：
{
     "code": 100033200,
     "message": "success",
     "userMessage": "success",
     "level": "success",
     "data": [
          {
               "id": 23245,
               "clusterID": 2173,
               "namespaceID": 3568,
               "miproxyServiceId": 0,
               "dbName": "app_stack",
               "remark": "",
               "proxy": "gaea",
               "region": "c3",
               "expiration": "",
               "is_exp": 0,
               "createTime": "2024-02-29T19:23:50+08:00",
               "updateTime": "2024-07-11T11:59:12+08:00",
               "iam_tree_id": 0,
               "iam_tree_path": "",
               "cluster_name": "pdl.b2c_service.gateway",
               "namespace_name": "c3_youpin01_b2c_app_stack",
               "show_type": "mysql"
          }
     ],
     "httpCode": null
}
错误返回：
{"code":100033403,"message":"forbidden","userMessage":"无权限","level":"warning","data":null,"HttpCode":403}

2、获取数据库表列表
/api/zeus/get_table_list
入参：
{
    "email": "yanyugang",
    "treeId": 12088,
    "databaseId": 78745,
    "proxy": "gaea"
}

返回：
{
     "code": 100033200,
     "message": "success",
     "userMessage": "success",
     "level": "success",
     "data": [
          {
               "id": 0,
               "is_protected": false,
               "table_name": "area",
               "user_list": null,
               "security_info": null,
               "owner": "dingpei"
          }
     ],
     "httpCode": null
}

错误返回：
{"code":100033403,"message":"forbidden","userMessage":"无权限","level":"warning","data":null,"HttpCode":403}

3、获取建表语句
/api/zeus/get_table_info
入参：
{
    "email": "yanyugang",
    "treeId": 12088,
    "databaseId": 78745,
    "proxy": "gaea",
    "tableName": "core_api_traffic"
}

返回：
{
     "code": 100033200,
     "message": "success",
     "userMessage": "success",
     "level": "success",
     "data": "CREATE TABLE `core_api_traffic` (\n  `id` bigint NOT NULL COMMENT 'ID',\n  `api` varchar(255) NOT NULL COMMENT 'api',\n  `threshold` int NOT NULL DEFAULT '2' COMMENT '阈值',\n  `alive` int NOT NULL DEFAULT '0' COMMENT '活动并发',\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API并发限流阈值设置表'",
     "httpCode": null
}
错误返回：
{"code":100033403,"message":"forbidden","userMessage":"无权限","level":"warning","data":null,"HttpCode":403}

4、获取我生成的文档列表
/api/rd-documents/list
入参：
{
    "pageNum": 1,
    "pageSize": 10
}

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 2,
    "pages": 1,
    "list": [
      {
        "id": 2,
        "uid": "yanyugang",
        "feishuUrl": "https://xiaomi.f.mioffice.cn/docx/doxk4tUhSuIJBkB8iVHTzwAJxvh",
        "prdContent": "这里是PRD文档内容...",
        "apiDocContent": "这里是API文档内容...",
        "sqlDocContent": "这里是SQL文档内容...",
        "resultContent": "这里是生成结果内容...",
        "createTime": "2025-04-24 13:54:37",
        "updateTime": "2025-04-24 13:54:37"
      }
    ]
  }
}
5、获取生成文档详情：
/api/rd-documents/1
入参：无
响应：
// 成功
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "uid": "yanyugang",
    "feishuUrl": "https://xiaomi.f.mioffice.cn/docx/doxk4tUhSuIJBkB8iVHTzwAJxvh",
    "prdContent": "这里是PRD文档内容...",
    "apiDocContent": "这里是API文档内容...",
    "sqlDocContent": "这里是SQL文档内容...",
    "resultContent": "这里是生成结果内容...",
    "createTime": "2025-04-24 13:39:44",
    "updateTime": "2025-04-24 13:39:44"
  }
}

// 失败
{
"code": 1,
"message": "文档不存在或无权限访问",
"data": [ ]
}
6、删除文档
put /api/rd-documents/1
入参：无
// 成功
{
  "code": 0,
  "message": "success",
  "data": null
}

// 失败
{
"code": 1,
"message": "文档不存在或无权限删除",
"data": null
}

7、根据meegoUrl获取PRD文档url
/api/getPrdUrlByMeegoUrl?meegoUrl=https://project.f.mioffice.cn/eegoest/story/detail/2546916
响应：
// 成功
{
     "code": 200,
     "message": "success",
     "data": {
          "prd_url": "https://xiaomi.f.mioffice.cn/docx/doxk4GnojbUHpAmfQNvsojRGgIm"
     }
}
// 失败
{
      code: 400,
      message: "参数错误：meegoUrl无法解析出workItemID",
      data: null
}
{
      code: 404,
      message: "未获取到有效的工作项详情",
      data: null
}

8、获取用户登录信息
/api/getUserInfo
响应：
{
  code: 200,
  message: "success",
  data: {
    userName: "xiaomi",
    name: "小米",
    email: "<EMAIL>",
    departmentName: "效能工具组"
  }
}

## 数据库

存储技术文档生成结果记录：

```sql
CREATE TABLE `rd_document_generation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `uid` varchar(20) NOT NULL COMMENT '用户ID',
  `meego_url` varchar(255) DEFAULT NULL COMMENT 'Meego需求URL',
  `feishu_url` text COMMENT '飞书文档URL',
  `prd_content` longtext COMMENT 'PRD内容',
  `api_doc_content` longtext COMMENT 'API文档内容',
  `sql_doc_content` longtext COMMENT 'SQL文档内容',
  `knowledge` longtext COMMENT '知识库导入信息',
  `result_content` longtext COMMENT '生成的结果内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技术文档生成结果记录表';

-- 修改feishu_url字段类型为varchar(255)
ALTER TABLE rd_document_generation MODIFY COLUMN feishu_url VARCHAR(255) NOT NULL DEFAULT '' COMMENT '飞书文档URL';

-- 添加rddoc_url字段
ALTER TABLE rd_document_generation ADD COLUMN rddoc_url VARCHAR(255) NOT NULL DEFAULT '' COMMENT '技术文档URL' AFTER feishu_url;
```

说明：
`id`: 自增主键（无符号类型）
`uid`: 用户ID，记录谁生成了该文档
`feishu_url`: 飞书文档URL，可选，默认为NULL
`rddoc_url`: 技术文档URL，可选，默认为NULL
`prd_content`: PRD内容，可选，默认为NULL（至少与feishu_url二选一）
`api_doc_content`: API文档内容，可选，默认为NULL
`sql_doc_content`: SQL文档内容，可选，默认为NULL
`knowledge`: 知识库导入信息，可选，默认为NULL
`result_content`: 生成的结果内容，默认为NULL
`meego_url`: Meego需求URL，默认为空字符串
`create_time`: 创建时间，默认为当前时间戳
`update_time`: 更新时间，默认为当前时间戳并在更新时自动更新