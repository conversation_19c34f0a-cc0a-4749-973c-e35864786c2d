FROM micr.cloud.mioffice.cn/wangzhidong1/java-image-tool AS tool
FROM micr.cloud.mioffice.cn/mixiao/jdk21-pandoc-mermaidfilter:0.0.2

# LABEL ENV_ID="_env_id" PROJECT_ID="_project_id" APPLICATION="_application" serverEnv="_server_env"
COPY --from=tool /opt/mione-curl /bin/mione-curl
COPY --from=tool /opt/init.sh /root/init.sh
COPY mone-codeflow-server/target/mone-codeflow-server-1.0.0-SNAPSHOT.jar /opt/app.jar
ENV TZ=Asia/Shanghai
WORKDIR /
USER root
RUN usermod -u 2000 work && groupmod -g 2001 work && usermod -g 2001 work
USER work
ENTRYPOINT ["bash","-c","bash /root/init.sh >/root/mishell.log 2>&1; cat /root/mishell.log ; exec java $JAVA_OPT $JAVA_OPT_EXT -jar /opt/app.jar"]
